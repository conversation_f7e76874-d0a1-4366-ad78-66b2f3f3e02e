<template>
  <div class="test-container">
    <h2>并行节点测试页面</h2>
    <div class="nodes-container">
      <!-- 并行分割节点测试 -->
      <div class="node-test">
        <h3>并行分割节点 (parallelSplit)</h3>
        <p>矩形 + 四个角的三角形指向外面</p>
        <svg width="300" height="200" style="border: 1px solid #ccc;">
          <FlowNode
            :node="{
              id: 'split1',
              label: '并行分割',
              type: 'parallelSplit',
              x: 50,
              y: 50,
              width: 200,
              height: 100
            }"
            :show-connection-points="true"
          />
        </svg>
      </div>

      <!-- 并行合并节点测试 -->
      <div class="node-test">
        <h3>并行合并节点 (parallelJoin)</h3>
        <p>矩形 + 四个角的三角形指向中心</p>
        <svg width="300" height="200" style="border: 1px solid #ccc;">
          <FlowNode
            :node="{
              id: 'join1',
              label: '并行合并',
              type: 'parallelJoin',
              x: 50,
              y: 50,
              width: 200,
              height: 100
            }"
            :show-connection-points="true"
          />
        </svg>
      </div>

      <!-- 选中状态测试 -->
      <div class="node-test">
        <h3>选中状态</h3>
        <svg width="300" height="200" style="border: 1px solid #ccc;">
          <FlowNode
            :node="{
              id: 'split2',
              label: '选中的分割',
              type: 'parallelSplit',
              x: 50,
              y: 50,
              width: 200,
              height: 100
            }"
            :selected="true"
            :show-connection-points="true"
          />
        </svg>
      </div>

      <!-- 高亮状态测试 -->
      <div class="node-test">
        <h3>高亮状态</h3>
        <svg width="300" height="200" style="border: 1px solid #ccc;">
          <FlowNode
            :node="{
              id: 'join2',
              label: '高亮的合并',
              type: 'parallelJoin',
              x: 50,
              y: 50,
              width: 200,
              height: 100
            }"
            :highlighted="true"
            :show-connection-points="true"
          />
        </svg>
      </div>

      <!-- 所有节点类型对比 -->
      <div class="node-test full-width">
        <h3>所有节点类型对比</h3>
        <svg width="100%" height="300" style="border: 1px solid #ccc;">
          <!-- 开始节点 -->
          <FlowNode
            :node="{
              id: 'start',
              label: '开始',
              type: 'start',
              x: 50,
              y: 100,
              width: 120,
              height: 80
            }"
            :show-connection-points="true"
          />
          
          <!-- 处理节点 -->
          <FlowNode
            :node="{
              id: 'process',
              label: '处理',
              type: 'process',
              x: 200,
              y: 100,
              width: 120,
              height: 80
            }"
            :show-connection-points="true"
          />
          
          <!-- 决策节点 -->
          <FlowNode
            :node="{
              id: 'decision',
              label: '决策',
              type: 'decision',
              x: 350,
              y: 100,
              width: 120,
              height: 80
            }"
            :show-connection-points="true"
          />
          
          <!-- 并行分割节点 -->
          <FlowNode
            :node="{
              id: 'split',
              label: '分割',
              type: 'parallelSplit',
              x: 500,
              y: 100,
              width: 120,
              height: 80
            }"
            :show-connection-points="true"
          />
          
          <!-- 并行合并节点 -->
          <FlowNode
            :node="{
              id: 'join',
              label: '合并',
              type: 'parallelJoin',
              x: 650,
              y: 100,
              width: 120,
              height: 80
            }"
            :show-connection-points="true"
          />
          
          <!-- 结束节点 -->
          <FlowNode
            :node="{
              id: 'end',
              label: '结束',
              type: 'end',
              x: 800,
              y: 100,
              width: 120,
              height: 80
            }"
            :show-connection-points="true"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import FlowNode from './FlowNode.vue';
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.nodes-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.node-test {
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.node-test.full-width {
  grid-column: 1 / -1;
}

.node-test h3 {
  margin: 0 0 12px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

svg {
  border-radius: 4px;
}
</style>
