<script lang="ts" setup>
import type { FormAttachmentProps } from './types';

import { computed, onMounted, reactive } from 'vue';

import { RefreshIcon } from '@vben-core/icons';

import { UploadButton } from '@coder/file-upload';
import { Button, Space, Table } from 'ant-design-vue';

import { swfOption } from '../../swfOption';
import { createFormAttachmentColumns } from './columns';
import { useFormAttachments } from './useFormAttachment';

const props = withDefaults(defineProps<FormAttachmentProps>(), {
  label: '附件',
  required: false,
});

const { del, fileList, list, getDownloadUrl, uploadUrl } =
  useFormAttachments(props);

const columns = reactive(
  createFormAttachmentColumns(
    (file) => del(file.fileId),
    props.isEdit,
    getDownloadUrl,
  ),
);

const headers = computed(() => {
  return {
    Authorization: `Bearer ${swfOption.getToken()}`,
  };
});

onMounted(() => {
  list();
});

const reLoad = () => {
  list();
};

defineExpose({ reLoad });
</script>

<template>
  <div>
    <Space v-if="props.isEdit" style="margin-bottom: 15px">
      <Button type="primary" @click="list()">
        <RefreshIcon />
        刷新
      </Button>
      <slot>
        <UploadButton
          :upload-url="uploadUrl"
          :headers="headers"
          file-form-key="file"
          @success="reLoad"
        />
      </slot>
    </Space>

    <Table
      :columns="columns"
      :data-source="fileList"
      :pagination="false"
      :row-key="(data: any) => data.fileId"
      bordered
      class="ant-table-striped"
      size="small"
      :scroll="{ y: 240 }"
    />

    <div v-if="fileList.length === 0" class="empty-placeholder">
      <span style="font-style: italic; color: #999">
        {{ props.isEdit ? '暂无附件，请上传' : '暂无附件' }}
      </span>
    </div>
  </div>
</template>

<style scoped>
.form-attachments .empty-placeholder {
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.form-attachments
  :deep(.ant-table-striped .ant-table-tbody > tr:nth-child(odd) > td) {
  background-color: #fafafa;
}
</style>
