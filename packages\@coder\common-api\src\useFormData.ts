import type { URLProvider } from './useDownload';

// 上传进度回调函数类型
type ProgressCallback = (event: ProgressEvent) => void;
// 提交前验证回调函数类型，返回true继续提交，false取消提交
type BeforeSubmitCallback = () => boolean;

// 请求头值提供器类型，可以是字符串或返回字符串的函数
type HeaderValueProvider = (() => string) | string;
type CompleteType = 'abort' | 'error' | 'success' | 'timeout';
export type FormSubmitResult = {
  complete: CompleteType;
  message?: string;
  response?: Response;
};

/**
 * 表单数据API类，提供文件上传和表单数据提交功能
 */
export class FormDataAPI {
  private beforeSubmitCallback?: BeforeSubmitCallback; // 提交前验证回调
  private formData: FormData; // 表单数据对象
  private headers: Map<string, HeaderValueProvider>; // 请求头映射
  private method: string; // HTTP请求方法
  private progressCallback?: ProgressCallback; // 上传进度回调

  /**
   * 请求超时时间（毫秒）
   * 如果设置了此属性，请求将在指定的毫秒数后自动超时
   */
  private timeout?: number;
  private urlProvider: URLProvider; // URL提供器
  private xhr?: XMLHttpRequest; // XMLHttpRequest对象

  /**
   * 构造函数
   * @param urlProvider URL提供器，可以是字符串、函数或异步函数
   * @param method HTTP请求方法，默认为POST
   */
  constructor(urlProvider: URLProvider, method: string = 'POST') {
    this.formData = new FormData();
    this.urlProvider = urlProvider;
    this.method = method.toUpperCase(); // 统一转换为大写
    this.headers = new Map();
  }

  /**
   * 添加提交前验证回调
   * @param callback 验证回调函数，返回true继续提交，false取消提交
   * @returns 返回当前实例，支持链式调用
   */
  addBeforeSubmitEvent(callback: BeforeSubmitCallback): FormDataAPI {
    this.beforeSubmitCallback = callback;
    return this;
  }

  /**
   * 添加表单数据字段
   * @param key 字段名
   * @param value 字段值
   * @returns 返回当前实例，支持链式调用
   */
  addData(key: string, value: string): FormDataAPI {
    this.formData.append(key, value);
    return this;
  }

  /**
   * 添加文件字段
   * @param key 字段名
   * @param file 文件对象
   * @returns 返回当前实例，支持链式调用
   */
  addFile(key: string, file: Blob | File): FormDataAPI {
    this.formData.append(key, file);
    return this;
  }

  /**
   * 添加请求头
   * @param headers 请求头对象，值可以是字符串或返回字符串的函数
   * @returns 返回当前实例，支持链式调用
   */
  addHeaders(
    headers: undefined | { [key: string]: HeaderValueProvider },
  ): FormDataAPI {
    if (!headers) return this;
    // 遍历请求头并添加到内部映射中
    for (const key in headers) {
      const value = headers[key];
      if (value) {
        this.headers.set(key, value);
      }
    }
    return this;
  }

  /**
   * 添加上传进度回调
   * @param callback 进度回调函数
   * @returns 返回当前实例，支持链式调用
   */
  addProgressEvent(callback: ProgressCallback): FormDataAPI {
    this.progressCallback = callback;
    return this;
  }

  /**
   * 取消上传
   * 如果当前有正在进行的请求，则中止该请求
   */
  cancel(): void {
    if (this.xhr && this.xhr.readyState !== XMLHttpRequest.DONE) {
      this.xhr.abort();
    }
  }

  /**
   * 设置请求超时时间
   * @param timeout 超时时间（毫秒）
   * @returns 返回当前实例，支持链式调用
   */
  setTimeout(timeout: number): FormDataAPI {
    this.timeout = timeout;
    return this;
  }

  /**
   * 提交表单数据
   * 执行提交前验证，然后发送HTTP请求
   */
  async submit(): Promise<FormSubmitResult> {
    const result = {
      complete: 'success',
      message: 'ok',
    } as FormSubmitResult;

    // 执行提交前验证，如果验证失败则直接返回
    if (this.beforeSubmitCallback && this.beforeSubmitCallback() === false) {
      result.complete = 'error';
      result.message = 'beforeSubmit error';
      throw result;
    }

    try {
      const url = await this.getUrl();

      return new Promise((resolve, reject) => {
        // 创建XMLHttpRequest对象
        this.xhr = new XMLHttpRequest();

        // 打开HTTP连接
        this.xhr.open(this.method, url);

        // 设置请求头
        this.headers.forEach((valueProvider, key) => {
          this.xhr?.setRequestHeader(key, this.getHeaderValue(valueProvider));
        });

        // 设置超时时间
        if (this.timeout) {
          this.xhr.timeout = this.timeout;
        }

        // 添加上传进度监听器
        if (this.progressCallback) {
          this.xhr.upload.addEventListener('progress', this.progressCallback);
        }

        // 添加响应完成监听器
        this.xhr.addEventListener('load', () => {
          if (!this.xhr) {
            result.complete = 'error';
            result.message = 'XHR instance is null';
            this.cleanup();
            return reject(result);
          }

          const resultResponse = new Response(this.xhr.response, {
            status: this.xhr.status,
          });

          result.complete =
            this.xhr.status >= 200 && this.xhr.status < 300
              ? 'success'
              : 'error';
          result.message = this.xhr.statusText || 'Request completed';
          result.response = resultResponse;

          this.cleanup(); // 清理资源
          resolve(result);
        });

        // 添加网络错误监听器
        this.xhr.addEventListener('error', () => {
          result.complete = 'error';
          result.message = 'Network error occurred';
          this.cleanup();
          reject(result);
        });

        // 添加请求中止监听器
        this.xhr.addEventListener('abort', () => {
          result.complete = 'abort';
          result.message = 'Request was aborted';
          this.cleanup();
          reject(result);
        });

        // 添加超时监听器
        this.xhr.addEventListener('timeout', () => {
          result.complete = 'timeout';
          result.message = 'Request timed out';
          this.cleanup();
          reject(result);
        });

        // 发送表单数据
        this.xhr.send(this.formData);
      });
    } catch (error) {
      // 捕获同步错误
      result.complete = 'error';
      result.message = `An error occurred during submission: ${error}`;
      this.cleanup();
      // eslint-disable-next-line unicorn/no-useless-promise-resolve-reject
      return Promise.reject(result);
    }
  }

  /**
   * 清理资源
   * 移除事件监听器并清空XHR对象引用
   */
  private cleanup(): void {
    if (this.xhr) {
      // 移除所有事件监听器
      if (this.progressCallback) {
        this.xhr.upload.removeEventListener('progress', this.progressCallback);
      }

      // 创建空函数用于移除事件监听器
      const emptyHandler = () => {};
      this.xhr.removeEventListener('load', emptyHandler);
      this.xhr.removeEventListener('error', emptyHandler);
      this.xhr.removeEventListener('abort', emptyHandler);
      this.xhr.removeEventListener('timeout', emptyHandler);
    }
    this.xhr = undefined;
  }

  /**
   * 获取请求头值
   * @param valueProvider 值提供器，可以是字符串或函数
   * @returns 请求头值
   */
  private getHeaderValue(valueProvider: HeaderValueProvider): string {
    return typeof valueProvider === 'string' ? valueProvider : valueProvider();
  }

  /**
   * 获取请求URL
   * @returns Promise包装的URL字符串
   */
  private async getUrl(): Promise<string> {
    if (typeof this.urlProvider === 'string') {
      return this.urlProvider;
    }

    // 调用URL提供器函数并等待结果
    const result = this.urlProvider();
    return await Promise.resolve(result);
  }
}

/**
 * 创建表单数据API实例的工厂函数
 * @param config 配置对象
 * @param config.url 提交URL，可以是字符串、函数或异步函数
 * @param config.method HTTP方法，可选，默认为POST
 * @returns FormDataAPI实例
 */
export function useFormData(config: {
  method?: 'delete' | 'get' | 'post' | 'put';
  url: URLProvider;
}): FormDataAPI {
  return new FormDataAPI(config.url, config.method);
}
