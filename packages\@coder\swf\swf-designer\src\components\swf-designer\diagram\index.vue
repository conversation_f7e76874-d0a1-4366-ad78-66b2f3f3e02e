<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';

import {
  AlignCenterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  AppstoreAddOutlined,
  CloseOutlined,
  SisternodeOutlined,
} from '@ant-design/icons-vue';
import { FlowChat } from '@coder/flow-chat';
import { Modal } from 'ant-design-vue';

import { useSwfDesigner } from '../hooks/index';
import { useDiagram } from './useDiagram';

const props = defineProps<{
  storeId: string;
}>();

const { createConditionDecision, createTask, deleteNode } = useSwfDesigner(
  props.storeId,
);

const { canBeDelete, nodes, selectNode, selectNodeText } = useDiagram(
  props.storeId,
);

const diagramRef = ref();

const graph = ref(0);

const onNodeClick = function (nodeClickEvent: {
  event: MouseEvent;
  node: { id: string };
}) {
  selectNode(nodeClickEvent.node.id);
};
const changeGraph = () => {
  let index = graph.value + 1;
  if (index > 3) {
    index = 0;
  }
  graph.value = index;
};
const confirmDelete = () => {
  Modal.confirm({
    onOk: () => {
      deleteNode(selectNodeText.value);
      selectNodeText.value = undefined;
    },
    title: '是否确认删除?',
  });
};

function setDynamicHeight() {
  const element = diagramRef.value;
  const rect = element.getBoundingClientRect();
  const windowHeight = window.innerHeight;
  const dynamicHeight = windowHeight - rect.top - 12;
  element.style.height = `${dynamicHeight}px`;
}
onMounted(() => {
  setDynamicHeight();
  // Set the height when the page loads
  window.addEventListener('load', setDynamicHeight);

  // Optionally, update the height when the window is resized
  window.addEventListener('resize', setDynamicHeight);
});

onUnmounted(() => {
  window.removeEventListener('load', setDynamicHeight);
  window.removeEventListener('resize', setDynamicHeight);
});
</script>

<template>
  <div
    ref="diagramRef"
    @click.stop="
      (event) => {
        event.preventDefault();
      }
    "
  >
    <div
      class="inline-flex items-center rounded-md border border-gray-200 bg-white"
    >
      <button
        class="p-1.5 text-gray-700 first:rounded-l-md last:rounded-r-md hover:bg-gray-50"
        title="添加任务"
        @click.stop="createTask"
      >
        <AppstoreAddOutlined class="h-4 w-4" />
      </button>

      <button
        class="p-1.5 text-gray-700 first:rounded-l-md last:rounded-r-md hover:bg-gray-50"
        title="添加判定器"
        @click.stop="() => createConditionDecision()"
      >
        <SisternodeOutlined class="h-4 w-4" />
      </button>

      <div
        @click.stop="
          (event) => {
            event.preventDefault();
          }
        "
      >
        <button
          :disabled="!canBeDelete()"
          :style="{
            'background-color': canBeDelete() ? '#fff' : '#f4f4f4',
          }"
          :title="selectNodeText"
          class="p-1.5 text-gray-700 first:rounded-l-md last:rounded-r-md hover:bg-gray-50"
          @click.stop="confirmDelete"
        >
          <CloseOutlined />
        </button>
      </div>

      <button
        class="p-1.5 text-gray-700 first:rounded-l-md last:rounded-r-md hover:bg-gray-50"
        @click.stop="changeGraph"
      >
        <AlignCenterOutlined v-if="graph === 0" />
        <AlignLeftOutlined v-if="graph === 1" />
        <AlignCenterOutlined v-if="graph === 2" :rotate="180" />
        <AlignRightOutlined v-if="graph === 3" />
      </button>
    </div>
    <FlowChat
      :data="nodes"
      :layout-direction="
        graph === 0 ? 'tb' : graph === 1 ? 'lr' : graph === 2 ? 'bt' : 'rl'
      "
      class="mermaid-coder"
      @node-click="onNodeClick"
    />
  </div>
</template>

<style>
.primary {
  background-color: #f4f4f4 !important;
}

.mermaid-coder {
  text-align: center;
  background-color: #fff;
}

.mermaid-coder svg {
  display: inline;
}
</style>
