import type { Flow<PERSON>hatData, FlowEdge, FlowNode } from '@coder/flow-chat';
import type {
  NodeSubmit,
  ScriptDecisionSubmit,
  SingleOutputNodeSubmit,
} from '@coder/swf-api';

import { workflowType } from '@coder/swf-api';

const getFlowNodeType = (nodeSubmit: NodeSubmit): FlowNode['type'] => {
  switch (nodeSubmit.$type) {
    case workflowType.boolScriptDecision:
    case workflowType.conditionDecision: {
      return 'decision';
    }
    case workflowType.endNode: {
      return 'end';
    }
    case workflowType.startNode: {
      return 'start';
    }
    case workflowType.workTask: {
      return 'process';
    }
    default: {
      return 'custom';
    }
  }
};

const getNodeColor = (nodeSubmit: NodeSubmit): string => {
  switch (nodeSubmit.$type) {
    case workflowType.boolScriptDecision: {
      return '#fbecb1';
    }
    case workflowType.endNode: {
      return '#f19386';
    }
    case workflowType.startNode: {
      return '#b8d5eb';
    }
    case workflowType.workTask: {
      return '#cde6b2';
    }
    default: {
      return '#ffffff';
    }
  }
};

const convertNodeSubmitToFlowNode = (nodeSubmit: NodeSubmit): FlowNode => {
  return {
    id: nodeSubmit.name,
    label: nodeSubmit.name,
    type: getFlowNodeType(nodeSubmit),
    data: {
      color: getNodeColor(nodeSubmit),
      editable: nodeSubmit.$type !== workflowType.endNode,
      nodeSubmit,
    },
  };
};

const generateFlowEdges = (nodes: NodeSubmit[]): FlowEdge[] => {
  const edges: FlowEdge[] = [];
  const nodeMap = new Map<string, NodeSubmit>();

  // 创建节点映射
  nodes.forEach((node) => {
    nodeMap.set(node.name, node);
  });

  nodes.forEach((node) => {
    // 处理普通的下一个节点连接
    const singleOutputNode = node as SingleOutputNodeSubmit;
    if (singleOutputNode.nextNodeName) {
      const edgeId = `${node.name}-${singleOutputNode.nextNodeName}`;
      let label = '';

      // 处理决策节点的标签
      if (node.$type === workflowType.boolScriptDecision) {
        const decision = node as ScriptDecisionSubmit;
        label = decision.matchDescription || '同意';
      }

      edges.push({
        id: edgeId,
        source: node.name,
        target: singleOutputNode.nextNodeName,
        label,
        type: 'straight',
      });
    }

    // 处理决策节点的 else 分支
    if (node.$type === workflowType.boolScriptDecision) {
      const decision = node as ScriptDecisionSubmit;
      if (decision.elseNodeName) {
        const edgeId = `${node.name}-${decision.elseNodeName}`;
        const label = decision.elseDescription || '拒绝';

        edges.push({
          id: edgeId,
          source: node.name,
          target: decision.elseNodeName,
          label,
          type: 'straight',
        });
      }
    }
  });

  return edges;
};

export const BuildFlowChatData = (submit: {
  nodes: NodeSubmit[];
}): FlowChatData => {
  if (!submit.nodes) {
    submit.nodes = [];
  }

  // 直接从 NodeSubmit 转换为 FlowNode
  const flowNodes = submit.nodes.map((node) =>
    convertNodeSubmitToFlowNode(node),
  );

  // 直接从 NodeSubmit 数组生成 FlowEdge
  const flowEdges = generateFlowEdges(submit.nodes);

  return {
    nodes: flowNodes,
    edges: flowEdges,
  };
};
