import type {
  ParallelJoinNodeSubmit,
  ParallelSplitNodeSubmit,
} from '@coder/swf-api';

import type { Editor } from '../../types';

import { AndroidFilled as JoinIcon } from '@ant-design/icons-vue';

import JoinEditor from '../../editors/parallel-join/parallel-join.vue';
import SplitEditor from '../../editors/parallel-split/parallel-split.vue';
import { useTabsStore } from '../../stores';
import { useEditorNav } from '../useEditorNav';

const editorNavFactory = useEditorNav();

export const addParallelJoinEditor = (
  storeId: string,
  submit: ParallelJoinNodeSubmit,
) => {
  const tabsStore = useTabsStore(storeId);
  const editor = {
    close: true,
    component: () => <JoinEditor storeId={storeId} value={submit}></JoinEditor>,
    icon: JoinIcon,
    key: editorNavFactory.builder.parallel.join(submit),
    title: () => submit.name,
  } as Editor;
  tabsStore.addEditor(editor, false);
};
export const addParallelSplitEditor = (
  storeId: string,
  submit: ParallelSplitNodeSubmit,
) => {
  const tabsStore = useTabsStore(storeId);
  const editor = {
    close: true,
    component: () => <SplitEditor storeId={storeId} value={submit} />,

    icon: JoinIcon,
    key: editorNavFactory.builder.parallel.split(submit),
    title: () => submit.name,
  } as Editor;
  tabsStore.addEditor(editor, false);
};
