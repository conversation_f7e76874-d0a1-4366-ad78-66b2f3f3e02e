<script setup lang="ts">
import FlowNode from './FlowNode.vue';
</script>

<template>
  <div class="example-container">
    <h2>并行节点示例</h2>
    <p>新的设计：矩形基础 + 角落三角形指示器</p>

    <div class="example-grid">
      <!-- parallelSplit 示例 -->
      <div class="example-item">
        <h3>parallelSplit（分割）</h3>
        <p>三角形指向中心，表示收集</p>
        <svg
          width="200"
          height="120"
          style="border: 1px solid #ddd; border-radius: 4px"
        >
          <FlowNode
            :node="{
              id: 'split',
              label: '分割',
              type: 'parallelSplit',
              x: 20,
              y: 20,
              width: 160,
              height: 80,
            }"
            :show-connection-points="true"
          />
        </svg>
      </div>

      <!-- parallelJoin 示例 -->
      <div class="example-item">
        <h3>parallelJoin（合并）</h3>
        <p>三角形指向外面，表示分散</p>
        <svg
          width="200"
          height="120"
          style="border: 1px solid #ddd; border-radius: 4px"
        >
          <FlowNode
            :node="{
              id: 'join',
              label: '合并',
              type: 'parallelJoin',
              x: 20,
              y: 20,
              width: 160,
              height: 80,
            }"
            :show-connection-points="true"
          />
        </svg>
      </div>

      <!-- 选中状态 -->
      <div class="example-item">
        <h3>选中状态</h3>
        <p>蓝色高亮边框</p>
        <svg
          width="200"
          height="120"
          style="border: 1px solid #ddd; border-radius: 4px"
        >
          <FlowNode
            :node="{
              id: 'selected',
              label: '已选中',
              type: 'parallelSplit',
              x: 20,
              y: 20,
              width: 160,
              height: 80,
            }"
            :selected="true"
            :show-connection-points="true"
          />
        </svg>
      </div>

      <!-- 高亮状态 -->
      <div class="example-item">
        <h3>高亮状态</h3>
        <p>脉冲动画效果</p>
        <svg
          width="200"
          height="120"
          style="border: 1px solid #ddd; border-radius: 4px"
        >
          <FlowNode
            :node="{
              id: 'highlighted',
              label: '高亮',
              type: 'parallelJoin',
              x: 20,
              y: 20,
              width: 160,
              height: 80,
            }"
            :highlighted="true"
            :show-connection-points="true"
          />
        </svg>
      </div>
    </div>

    <!-- 工作流示例 -->
    <div class="workflow-example">
      <h3>工作流示例</h3>
      <p>展示并行节点在实际工作流中的使用</p>
      <svg
        width="100%"
        height="200"
        style="border: 1px solid #ddd; border-radius: 4px"
      >
        <!-- 开始 -->
        <FlowNode
          :node="{
            id: 'start',
            label: '开始',
            type: 'start',
            x: 50,
            y: 60,
            width: 100,
            height: 60,
          }"
        />

        <!-- 并行分割 -->
        <FlowNode
          :node="{
            id: 'split',
            label: '分割',
            type: 'parallelSplit',
            x: 200,
            y: 60,
            width: 100,
            height: 60,
          }"
        />

        <!-- 任务1 -->
        <FlowNode
          :node="{
            id: 'task1',
            label: '任务1',
            type: 'process',
            x: 350,
            y: 20,
            width: 100,
            height: 60,
          }"
        />

        <!-- 任务2 -->
        <FlowNode
          :node="{
            id: 'task2',
            label: '任务2',
            type: 'process',
            x: 350,
            y: 100,
            width: 100,
            height: 60,
          }"
        />

        <!-- 并行合并 -->
        <FlowNode
          :node="{
            id: 'join',
            label: '合并',
            type: 'parallelJoin',
            x: 500,
            y: 60,
            width: 100,
            height: 60,
          }"
        />

        <!-- 结束 -->
        <FlowNode
          :node="{
            id: 'end',
            label: '结束',
            type: 'end',
            x: 650,
            y: 60,
            width: 100,
            height: 60,
          }"
        />

        <!-- 连接线（简化显示） -->
        <line
          x1="150"
          y1="90"
          x2="200"
          y2="90"
          stroke="#666"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />
        <line
          x1="300"
          y1="90"
          x2="350"
          y2="50"
          stroke="#666"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />
        <line
          x1="300"
          y1="90"
          x2="350"
          y2="130"
          stroke="#666"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />
        <line
          x1="450"
          y1="50"
          x2="500"
          y2="90"
          stroke="#666"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />
        <line
          x1="450"
          y1="130"
          x2="500"
          y2="90"
          stroke="#666"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />
        <line
          x1="600"
          y1="90"
          x2="650"
          y2="90"
          stroke="#666"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />

        <!-- 箭头标记 -->
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
          </marker>
        </defs>
      </svg>
    </div>
  </div>
</template>

<style scoped>
.example-container {
  max-width: 1200px;
  padding: 20px;
  margin: 0 auto;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.example-item {
  text-align: center;
}

.example-item h3 {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.example-item p {
  margin: 0 0 12px;
  font-size: 14px;
  color: #6b7280;
}

.workflow-example {
  margin-top: 40px;
  text-align: center;
}

.workflow-example h3 {
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.workflow-example p {
  margin: 0 0 16px;
  font-size: 14px;
  color: #6b7280;
}

svg {
  background: #fafafa;
}
</style>
