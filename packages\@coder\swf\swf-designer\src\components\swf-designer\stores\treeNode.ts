// 导入工作流节点类型定义
import type {
  ConditionDecisionSubmit,
  NodeSubmit,
  ParallelJoinNodeSubmit,
  ParallelSplitNodeSubmit,
  ScriptDecisionSubmit,
  WorkTaskSubmit,
} from '@coder/swf-api';

// 导入树节点类型定义
import type { SwfTreeNode } from '../taskTreeNav/useTree';

// Vue 3 响应式系统
import { reactive } from 'vue';

// 工作流类型枚举和UI组件
import { workflowType } from '@coder/swf-api';
import { message } from 'ant-design-vue';
import { defineStore } from 'pinia';

// 导入自定义hooks
import { useEditorNav } from '../hooks/useEditorNav';
import { useTreeBuilder } from '../taskTreeNav/useTree';

/**
 * 树节点存储类型定义
 * 定义了树节点操作的所有方法和属性
 */
export type TreeNodeStoreType = {
  /** 添加新节点到树中 */
  addNode: (
    node:
      | ConditionDecisionSubmit
      | ParallelJoinNodeSubmit
      | ParallelSplitNodeSubmit
      | ScriptDecisionSubmit
      | WorkTaskSubmit,
    selectIt?: boolean,
  ) => void;

  /** 构建整个树结构 */
  build: () => void;

  /** 展开的节点键值数组 */
  expandedKeys: string[];

  /**
   * 重建节点的导航键
   * @param node - 需要重建的节点
   */
  rebuildNavKey: (node: NodeSubmit) => void;

  /** 删除指定节点 */
  removeNode(deleteNode: NodeSubmit): void;

  /** 选中的节点键值数组 */
  selectedKeys: string[];

  /** 树节点数据数组 */
  treeNodes: SwfTreeNode[];
};

// 存储所有树节点store实例的映射表
const _storageMap = new Map<string, TreeNodeStoreType>();

/**
 * 递归遍历树节点的工具函数
 * 从指定的起始节点开始，递归遍历所有子节点并执行回调函数
 * @param startNode - 起始节点
 * @param callback - 对每个子节点执行的回调函数
 */
const _treeFullScanAction = (
  startNode: SwfTreeNode,
  callback: (child: SwfTreeNode) => void,
) => {
  startNode.children?.forEach((child) => {
    // 对当前子节点执行回调
    callback(child);

    // 如果子节点还有子节点，则递归处理
    const childrenLength = child.children?.length;
    if (childrenLength !== undefined && childrenLength > 0) {
      _treeFullScanAction(child, callback);
    }
  });
};

/**
 * 创建树节点存储的工厂函数
 * @param treeStoreId - 树存储的唯一标识符
 * @returns Pinia store 定义
 */
const createTreeStore = (treeStoreId: string) => {
  return defineStore(treeStoreId, () => {
    // 获取树构建器的各种创建方法
    const {
      buildTree,
      createJsDecision,
      createWorkTask,
      createParallelJoin,
      createParallelSplit,
    } = useTreeBuilder(treeStoreId);

    // 响应式数据定义
    const treeNodes = reactive<SwfTreeNode[]>([]); // 树节点数据
    const selectedKeys = reactive<string[]>([]); // 选中的节点键值
    const expandedKeys = reactive<string[]>([]); // 展开的节点键值

    return {
      /**
       * 添加新节点到树中
       * @param node - 要添加的节点数据
       * @param selectIt - 是否选中新添加的节点，默认为true
       */
      addNode: (
        node:
          | ConditionDecisionSubmit
          | ParallelJoinNodeSubmit
          | ParallelSplitNodeSubmit
          | ScriptDecisionSubmit
          | WorkTaskSubmit,
        selectIt: boolean = true,
      ) => {
        let addedNode: SwfTreeNode | undefined;
        const root = treeNodes[0];

        // 确保根节点存在
        if (!root) return;

        // 确保根节点有children数组
        if (!root.children) {
          root.children = [];
        }

        // 根据节点类型创建对应的树节点
        switch (node.$type) {
          case workflowType.boolScriptDecision: {
            addedNode = createJsDecision(node as ScriptDecisionSubmit);
            root.children.push(addedNode);
            break;
          }
          case workflowType.parallelJoin: {
            addedNode = createParallelJoin(node as ParallelJoinNodeSubmit);
            root.children.push(addedNode);
            break;
          }
          case workflowType.parallelSplit: {
            addedNode = createParallelSplit(node as ParallelSplitNodeSubmit);
            root.children.push(addedNode);
            break;
          }
          case workflowType.workTask: {
            addedNode = createWorkTask(node as WorkTaskSubmit);
            root.children.push(addedNode);
            break;
          }
        }

        // 如果需要选中新节点且节点创建成功
        if (selectIt && addedNode) {
          expandedKeys.push(addedNode.key);
          selectedKeys.splice(0);
          selectedKeys.push(addedNode.key);
        }
      },
      /**
       * 构建整个树结构
       * 清空现有树节点，重新构建根节点，并设置默认的展开和选中状态
       */
      build: () => {
        // 清空现有的树节点
        treeNodes.splice(0);

        // 构建新的树结构
        treeNodes.push(buildTree());
        const rootTreeNode = treeNodes[0];

        if (!rootTreeNode) return;

        // 设置根节点为展开状态
        expandedKeys.splice(0);
        expandedKeys.push(rootTreeNode.key);

        // 设置根节点为选中状态
        selectedKeys.splice(0);
        selectedKeys.push(rootTreeNode.key);
      },

      // 导出响应式数据
      expandedKeys,

      /**
       * 重建节点的导航键
       * 当节点信息发生变化时，重新创建节点并更新树结构
       * @param node - 需要重建的节点
       */
      rebuildNavKey: (node: NodeSubmit) => {
        const storeId = treeStoreId.split('/')[0];

        // 获取节点创建方法
        const { createJsDecision, createWorkTask } = useTreeBuilder(
          storeId as string,
        );

        let newNode: SwfTreeNode | undefined;
        let oldNode: SwfTreeNode | undefined;

        // 根据节点类型重新创建节点
        switch (node.$type) {
          case workflowType.boolScriptDecision: {
            newNode = createJsDecision(node as ScriptDecisionSubmit);
            // 注意：这里使用的是 decison（原代码中的拼写），保持与现有代码一致
            oldNode = treeNodes[0]?.children?.find((_) => _.decison === node);
            break;
          }
          case workflowType.workTask: {
            newNode = createWorkTask(node as WorkTaskSubmit);
            oldNode = treeNodes[0]?.children?.find((_) => _.worktask === node);
            break;
          }
        }

        // 如果找到旧节点，则更新其内容
        if (oldNode) {
          oldNode.children?.splice(0);
          if (newNode) {
            Object.assign(oldNode, newNode);
          }
        }
      },
      /**
       * 删除指定节点
       * @param node - 要删除的节点
       */
      removeNode: (node: NodeSubmit) => {
        // 构建节点的唯一键值
        const key = useEditorNav().build(node);

        // 在根节点的子节点中查找要删除的节点
        const index = treeNodes[0]?.children?.findIndex((_) => _.key === key);

        if (index === undefined || index === -1) {
          // 删除失败，显示错误消息
          message.error({ content: `删除失败: ${node.name}` });
        } else {
          // 从树中移除节点
          treeNodes[0]?.children?.splice(index, 1);
        }
      },

      // 导出响应式数据
      selectedKeys,
      treeNodes,
    };
  });
};

/**
 * 获取树节点存储实例的Hook函数
 * 使用单例模式确保相同storeId只创建一个store实例
 * @param storeId - 存储的唯一标识符
 * @returns 树节点存储实例
 * @throws {Error} 当storeId为空或未定义时抛出错误
 */
export const useTreeNodeStore = (storeId: string): TreeNodeStoreType => {
  // 参数验证
  if (storeId === undefined || storeId === '') {
    throw new Error('storeId不能为空或未定义');
  }

  // 构建设计器存储的唯一标识
  const designerStoreId = `${storeId}/tree`;

  // 检查是否已存在该store实例
  if (_storageMap.has(designerStoreId)) {
    return _storageMap.get(designerStoreId) as TreeNodeStoreType;
  }

  // 创建新的store实例
  const useStore = createTreeStore(storeId);
  const store = useStore();

  // 缓存store实例
  _storageMap.set(designerStoreId, store);

  return store as TreeNodeStoreType;
};
