import type { SingleOutputNodeSubmit } from './NodeSubmit';

export type DecisionSubmit = SingleOutputNodeSubmit & {
  matchDescription?: string;
};
export type ScriptDecisionSubmit = {
  /**
   * 拒绝说明
   */
  elseDescription?: string;

  /**
   * 任务名称
   */
  elseNodeName?: string;
  /**
   * 脚本
   */
  script: string;
} & DecisionSubmit;

export type ConditionSettingSubmit = {
  description: string;
  id: number;
  matchValue: string;
  nodeName: string;
};

export type ConditionDecisionSubmit = {
  /**
   * 脚本
   */
  script: string;
  /**
   * 设置多个条件
   */
  settings: Array<ConditionSettingSubmit>;
} & DecisionSubmit;
