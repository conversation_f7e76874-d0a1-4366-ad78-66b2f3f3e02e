import type {
  MultiOutputNodeSubmit,
  SingleOutputNodeSubmit,
} from './NodeSubmit';

export interface ParallelJoinNodeSubmit extends SingleOutputNodeSubmit {
  customJoinScript: string;
  joinCondition: JoinConditionSubmit;
  waitForWorkTasks: string[];
}
export interface ParallelSplitNodeSubmit extends MultiOutputNodeSubmit {
  comment: string | undefined;
}
export enum JoinConditionSubmit {
  All,
  Any,
  Custom,
}
