export type WorkflowType =
  | 'BoolScriptDecision'
  | 'ConditionScriptDecision'
  | 'EndNode'
  | 'PreviousCommand'
  | 'ScriptAssigner'
  | 'ScriptCommand'
  | 'StartNode'
  | 'UsersAssigner'
  | 'WorkTask';
export type AssignerType = 'ScriptAssigner' | 'UsersAssigner';

export const workflowType = {
  boolScriptDecision: 'BoolScriptDecision',
  conditionDecision: 'ConditionScriptDecision',
  endNode: 'EndNode',
  scriptAssigner: 'ScriptAssigner',
  usersAssigner: 'UsersAssigner',
  startNode: 'StartNode',
  workTask: 'WorkTask',
  workTaskCommandPrevious: 'PreviousCommand',
  workTaskCommandScript: 'ScriptCommand',
  parallelJoin: 'ParallelJoinNoe',
  parallelSplit: 'ParallelSplitNode',
  // scriptCommands:
  //   'System.Collections.Generic.List`1[[Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskScriptCommandSubmit, Coder.ScriptWorkflow.Abstractions]], System.Private.CoreLib'
};
// Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskSubmit
