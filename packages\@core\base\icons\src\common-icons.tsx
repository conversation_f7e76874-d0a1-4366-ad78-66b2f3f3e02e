import { defineComponent, h } from 'vue';

import AntIcon, { LoadingOutlined } from '@ant-design/icons-vue';

import { createIconifyIcon } from './index';

export const createAntDesignIcon = (icon: string) => {
  return defineComponent({
    name: `Icon-${icon}-antd`,
    setup() {
      const LuicIcon = createIconifyIcon(icon);

      const slots = {
        component: () => h(LuicIcon, {}),
      };

      return () => h(AntIcon, {}, slots);
    },
  });
};
export const EditIcon = createAntDesignIcon('lucide:pencil');
export const DeleteIcon = createAntDesignIcon('lucide:trash-2');
export const AddIcon = createAntDesignIcon('mdi:plus');
export const RefreshIcon = createAntDesignIcon('lucide:rotate-ccw');
export const SaveIcon = createAntDesignIcon('lucide:save');
export const LoadingIcon = LoadingOutlined;
export const CloseIcon = createAntDesignIcon('lucide:x');
export const PubMessageIcon = createAntDesignIcon('lucide:message-circle-more');
export const UploadIcon = createAntDesignIcon('mdi:upload');
export const UsersIcon = createAntDesignIcon('mdi:account-group');
export const PreviewIcon = createAntDesignIcon('mdi:eye-outline');
export const CheckIcon = createAntDesignIcon('mdi:check');
