<script setup lang="ts">
import type { FlowNode, NodeClickEvent } from '../types';

import { computed, ref } from 'vue';

interface Props {
  highlighted?: boolean; // 新增：外部指定的高亮状态
  node: FlowNode;
  selected?: boolean;
  showConnectionPoints?: boolean;
}

interface Emits {
  (e: 'click', event: NodeClickEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  highlighted: false,
  showConnectionPoints: false,
});

const emit = defineEmits<Emits>();

const isHovered = ref(false);

const handleClick = (event: MouseEvent) => {
  emit('click', { node: props.node, event });
};

// 计算节点的宽度和高度
const nodeWidth = computed(() => props.node.width || 160 * 1.5);
const nodeHeight = computed(() => props.node.height || 60 * 1.5);

// 计算中心点
const centerX = computed(() => nodeWidth.value / 2);
const centerY = computed(() => nodeHeight.value / 2);

const getNodeRadius = () => {
  switch (props.node.type) {
    case 'decision': {
      return 8;
    }
    case 'end':
    case 'start': {
      return 30;
    }
    case 'parallelJoin':
    case 'parallelSplit': {
      return 4; // 并行节点使用较小的圆角
    }
    default: {
      return 6;
    }
  }
};

const getNodeClass = () => {
  const classes = ['node-shape'];

  if (props.selected) classes.push('node-selected');
  else if (props.highlighted) classes.push('node-highlighted');

  if (isHovered.value) classes.push('node-hovered');
  if (props.node.type) classes.push(`node-${props.node.type}`);

  return classes.join(' ');
};

const getNodeFill = () => {
  // 选中状态优先级最高
  if (props.selected) return '#e0e7ff';

  // 高亮状态优先级其次
  if (props.highlighted) {
    switch (props.node.type) {
      case 'decision': {
        return '#ffdc50';
      } // 更深的黄色
      case 'end': {
        return '#f87171';
      } // 更深的红色
      case 'parallelJoin': {
        return '#c084fc';
      } // 更深的紫色
      case 'parallelSplit': {
        return '#fb923c';
      } // 更深的橙色
      case 'process': {
        /* return '#d1d5db';*/
        return '#cde678';
      } // 更深的灰色
      case 'start': {
        return '#89c4f1';
      } // 更深的绿色
      default: {
        return '#d1d5db';
      }
    }
  }

  // 悬停状态
  if (isHovered.value) return '#f1f5f9';

  // 默认颜色
  switch (props.node.type) {
    case 'decision': {
      return '#fbecb1';
    }
    case 'end': {
      return '#f19386';
    }
    case 'parallelJoin': {
      return '#ddd6fe';
    } // 浅紫色
    case 'parallelSplit': {
      return '#fed7aa';
    } // 浅橙色
    case 'process': {
      return '#cde6b2';
    }
    case 'start': {
      return '#b8d5eb';
    }
    default: {
      return '#f8fafc';
    }
  }
};

const getNodeStroke = () => {
  // 选中状态优先级最高
  if (props.selected) return '#4f46e5';

  // 高亮状态优先级其次
  if (props.highlighted) {
    switch (props.node.type) {
      case 'decision': {
        return '#f59e0b';
      } // 更深的黄色边框
      case 'end': {
        return '#ef4444';
      } // 更深的红色边框
      case 'parallelJoin': {
        return '#9333ea';
      } // 更深的紫色边框
      case 'parallelSplit': {
        return '#ea580c';
      } // 更深的橙色边框
      case 'process': {
        return '#6b7280';
      } // 更深的灰色边框
      case 'start': {
        return '#000';
      } // 更深的绿色边框
      default: {
        return '#6b7280';
      }
    }
  }

  // 悬停状态
  if (isHovered.value) return '#64748b';

  // 默认颜色
  switch (props.node.type) {
    case 'decision': {
      return '#d97706';
    }
    case 'end': {
      return '#dc2626';
    }
    case 'parallelJoin': {
      return '#a855f7';
    } // 紫色边框
    case 'parallelSplit': {
      return '#f97316';
    } // 橙色边框
    case 'process': {
      return '#94a3b8';
    }
    case 'start': {
      return '#000';
    }
    default: {
      return '#94a3b8';
    }
  }
};

const getTextClass = () => {
  const classes = ['node-text'];
  if (props.node.type) classes.push(`text-${props.node.type}`);
  return classes.join(' ');
};

// 计算菱形的路径
const getDiamondPath = () => {
  const w = nodeWidth.value;
  const h = nodeHeight.value;
  return `M ${w / 2} 0 L ${w} ${h / 2} L ${w / 2} ${h} L 0 ${h / 2} Z`;
};

// 计算并行分割节点的三角形（指向外面）
const getParallelSplitTriangles = () => {
  const w = nodeWidth.value;
  const h = nodeHeight.value;
  const triangleSize = 12;
  const offset = 8; // 距离角落的偏移

  return [
    // 左上角三角形 - 指向左上
    `M ${offset} ${offset + triangleSize} L ${offset + triangleSize} ${offset} L ${offset + triangleSize} ${offset + triangleSize} Z`,
    // 右上角三角形 - 指向右上
    `M ${w - offset - triangleSize} ${offset} L ${w - offset} ${offset} L ${w - offset - triangleSize} ${offset + triangleSize} Z`,
    // 右下角三角形 - 指向右下
    `M ${w - offset - triangleSize} ${h - offset - triangleSize} L ${w - offset} ${h - offset} L ${w - offset - triangleSize} ${h - offset} Z`,
    // 左下角三角形 - 指向左下
    `M ${offset} ${h - offset - triangleSize} L ${offset + triangleSize} ${h - offset - triangleSize} L ${offset + triangleSize} ${h - offset} Z`,
  ];
};

// 计算并行合并节点的三角形（指向中心）
const getParallelJoinTriangles = () => {
  const w = nodeWidth.value;
  const h = nodeHeight.value;
  const triangleSize = 12;
  const offset = 8; // 距离角落的偏移

  return [
    // 左上角三角形 - 指向中心
    `M ${offset} ${offset} L ${offset + triangleSize} ${offset} L ${offset} ${offset + triangleSize} Z`,
    // 右上角三角形 - 指向中心
    `M ${w - offset} ${offset} L ${w - offset - triangleSize} ${offset} L ${w - offset} ${offset + triangleSize} Z`,
    // 右下角三角形 - 指向中心
    `M ${w - offset} ${h - offset} L ${w - offset} ${h - offset - triangleSize} L ${w - offset - triangleSize} ${h - offset} Z`,
    // 左下角三角形 - 指向中心
    `M ${offset} ${h - offset} L ${offset + triangleSize} ${h - offset} L ${offset} ${h - offset - triangleSize} Z`,
  ];
};

// 计算连接点位置
const getConnectionPoints = () => {
  const w = nodeWidth.value;
  const h = nodeHeight.value;

  switch (props.node.type) {
    case 'decision': {
      // 菱形的连接点在四个顶点
      return {
        top: { x: w / 2, y: 0 },
        bottom: { x: w / 2, y: h },
        left: { x: 0, y: h / 2 },
        right: { x: w, y: h / 2 },
      };
    }
    case 'end':
    case 'start': {
      // 圆形的连接点在上下
      const radius = Math.min(w, h) / 2;
      const centerX = w / 2;
      const centerY = h / 2;
      return {
        top: { x: centerX, y: centerY - radius },
        bottom: { x: centerX, y: centerY + radius },
      };
    }
    case 'parallelJoin':
    case 'parallelSplit': {
      // 并行节点：矩形的四边都有连接点，与决策节点类似
      return {
        top: { x: w / 2, y: 0 },
        bottom: { x: w / 2, y: h },
        left: { x: 0, y: h / 2 },
        right: { x: w, y: h / 2 },
      };
    }
    default: {
      // 矩形的连接点在上下
      return {
        top: { x: w / 2, y: 0 },
        bottom: { x: w / 2, y: h },
      };
    }
  }
};

const connectionPoints = computed(() => getConnectionPoints());
</script>

<template>
  <g
    class="flow-node"
    :transform="`translate(${node.x || 0}, ${node.y || 0})`"
    @click="handleClick"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- 不同类型的节点形状 -->

    <!-- 开始和结束节点 - 圆形 -->
    <circle
      v-if="node.type === 'start' || node.type === 'end'"
      :cx="centerX"
      :cy="centerY"
      :r="Math.min(nodeWidth, nodeHeight) / 2"
      :class="getNodeClass()"
      :fill="getNodeFill()"
      :stroke="getNodeStroke()"
      stroke-width="2"
    />

    <!-- 决策节点 - 菱形 -->
    <path
      v-else-if="node.type === 'decision'"
      :d="getDiamondPath()"
      :class="getNodeClass()"
      :fill="getNodeFill()"
      :stroke="getNodeStroke()"
      stroke-width="2"
    />

    <!-- 并行分割节点 - 矩形 + 向外三角形 -->
    <template v-else-if="node.type === 'parallelSplit'">
      <!-- 基础矩形 -->
      <rect
        :width="nodeWidth"
        :height="nodeHeight"
        :rx="getNodeRadius()"
        :ry="getNodeRadius()"
        :class="getNodeClass()"
        :fill="getNodeFill()"
        :stroke="getNodeStroke()"
        stroke-width="2"
      />
      <!-- 四个向外的三角形 -->
      <path
        v-for="(trianglePath, index) in getParallelSplitTriangles()"
        :key="`split-triangle-${index}`"
        :d="trianglePath"
        :fill="getNodeStroke()"
        class="parallel-triangle"
      />
    </template>

    <!-- 并行合并节点 - 矩形 + 向内三角形 -->
    <template v-else-if="node.type === 'parallelJoin'">
      <!-- 基础矩形 -->
      <rect
        :width="nodeWidth"
        :height="nodeHeight"
        :rx="getNodeRadius()"
        :ry="getNodeRadius()"
        :class="getNodeClass()"
        :fill="getNodeFill()"
        :stroke="getNodeStroke()"
        stroke-width="2"
      />
      <!-- 四个向内的三角形 -->
      <path
        v-for="(trianglePath, index) in getParallelJoinTriangles()"
        :key="`join-triangle-${index}`"
        :d="trianglePath"
        :fill="getNodeStroke()"
        class="parallel-triangle"
      />
    </template>

    <!-- 处理节点和其他 - 矩形 -->
    <rect
      v-else
      :width="nodeWidth"
      :height="nodeHeight"
      :rx="getNodeRadius()"
      :ry="getNodeRadius()"
      :class="getNodeClass()"
      :fill="getNodeFill()"
      :stroke="getNodeStroke()"
      stroke-width="2"
    />

    <!-- 节点文本 -->
    <text
      :x="centerX"
      :y="centerY"
      text-anchor="middle"
      dominant-baseline="middle"
      :class="getTextClass()"
      :font-size="18"
      fill="currentColor"
    >
      {{ node.label }}
    </text>

    <!-- 连接点 -->
    <template v-if="showConnectionPoints">
      <!-- 通用上连接点 -->
      <circle
        v-if="connectionPoints.top"
        :cx="connectionPoints.top.x"
        :cy="connectionPoints.top.y"
        r="4"
        class="connection-point connection-point-top"
        fill="#4f46e5"
      />

      <!-- 通用下连接点 -->
      <circle
        v-if="connectionPoints.bottom"
        :cx="connectionPoints.bottom.x"
        :cy="connectionPoints.bottom.y"
        r="4"
        class="connection-point connection-point-bottom"
        fill="#4f46e5"
      />

      <!-- 左连接点（菱形和并行节点） -->
      <circle
        v-if="
          connectionPoints.left &&
          (node.type === 'decision' ||
            node.type === 'parallelSplit' ||
            node.type === 'parallelJoin')
        "
        :cx="connectionPoints.left.x"
        :cy="connectionPoints.left.y"
        r="4"
        class="connection-point connection-point-left"
        :fill="
          node.type === 'parallelSplit'
            ? '#f97316'
            : node.type === 'parallelJoin'
              ? '#a855f7'
              : '#4f46e5'
        "
      />

      <!-- 右连接点（菱形和并行节点） -->
      <circle
        v-if="
          connectionPoints.right &&
          (node.type === 'decision' ||
            node.type === 'parallelSplit' ||
            node.type === 'parallelJoin')
        "
        :cx="connectionPoints.right.x"
        :cy="connectionPoints.right.y"
        r="4"
        class="connection-point connection-point-right"
        :fill="
          node.type === 'parallelSplit'
            ? '#f97316'
            : node.type === 'parallelJoin'
              ? '#a855f7'
              : '#4f46e5'
        "
      />
    </template>
  </g>
</template>

<style scoped>
@keyframes highlight-pulse {
  0%,
  100% {
    filter: drop-shadow(0 3px 8px rgb(59 130 246 / 40%));
  }

  50% {
    filter: drop-shadow(0 4px 12px rgb(59 130 246 / 60%));
  }
}

.flow-node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.node-shape {
  transition: all 0.2s ease;
}

.node-selected {
  filter: drop-shadow(0 4px 6px rgb(79 70 229 / 30%));
}

.node-highlighted {
  filter: drop-shadow(0 3px 8px rgb(59 130 246 / 40%));
  animation: highlight-pulse 2s ease-in-out infinite;
}

.node-hovered {
  filter: drop-shadow(0 2px 4px rgb(0 0 0 / 10%));
}

.node-text {
  font-weight: 500;
  pointer-events: none;
  user-select: none;
}

.connection-point {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.flow-node:hover .connection-point {
  opacity: 1;
}

/* 不同节点类型的特殊样式 */
.node-start,
.node-end {
  /* 椭圆形节点的特殊样式 */
}

.node-decision {
  /* 菱形节点的特殊样式 */
}

.node-process {
  /* 矩形节点的特殊样式 */
}

.node-parallelSplit,
.node-parallelJoin {
  /* 并行节点的特殊样式 */
  filter: drop-shadow(0 2px 4px rgb(0 0 0 / 15%));
}

.node-parallelSplit:hover,
.node-parallelJoin:hover {
  filter: drop-shadow(0 4px 8px rgb(0 0 0 / 20%));
}

.parallel-triangle {
  /* 并行节点三角形的样式 */
  transition: all 0.2s ease;
}

.flow-node:hover .parallel-triangle {
  /* 悬停时三角形的效果 */
  opacity: 0.8;
}
</style>
