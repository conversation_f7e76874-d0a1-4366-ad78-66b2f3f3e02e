<script setup lang="ts">
import type { FlowNode, NodeClickEvent } from '../types';

import { computed, ref } from 'vue';

interface Props {
  highlighted?: boolean; // 新增：外部指定的高亮状态
  node: FlowNode;
  selected?: boolean;
  showConnectionPoints?: boolean;
}

interface Emits {
  (e: 'click', event: NodeClickEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  highlighted: false,
  showConnectionPoints: false,
});

const emit = defineEmits<Emits>();

const isHovered = ref(false);

const handleClick = (event: MouseEvent) => {
  emit('click', { node: props.node, event });
};

// 计算节点的宽度和高度
const nodeWidth = computed(() => props.node.width || 160 * 1.5);
const nodeHeight = computed(() => props.node.height || 60 * 1.5);

// 计算中心点
const centerX = computed(() => nodeWidth.value / 2);
const centerY = computed(() => nodeHeight.value / 2);

const getNodeRadius = () => {
  switch (props.node.type) {
    case 'decision': {
      return 8;
    }
    case 'end':
    case 'start': {
      return 30;
    }
    default: {
      return 6;
    }
  }
};

const getNodeClass = () => {
  const classes = ['node-shape'];

  if (props.selected) classes.push('node-selected');
  else if (props.highlighted) classes.push('node-highlighted');

  if (isHovered.value) classes.push('node-hovered');
  if (props.node.type) classes.push(`node-${props.node.type}`);

  return classes.join(' ');
};

const getNodeFill = () => {
  // 选中状态优先级最高
  if (props.selected) return '#e0e7ff';

  // 高亮状态优先级其次
  if (props.highlighted) {
    switch (props.node.type) {
      case 'decision': {
        return '#ffdc50';
      } // 更深的黄色
      case 'end': {
        return '#f87171';
      } // 更深的红色
      case 'process': {
        /* return '#d1d5db';*/
        return '#cde678';
      } // 更深的灰色
      case 'start': {
        return '#89c4f1';
      } // 更深的绿色
      default: {
        return '#d1d5db';
      }
    }
  }

  // 悬停状态
  if (isHovered.value) return '#f1f5f9';

  // 默认颜色
  switch (props.node.type) {
    case 'decision': {
      return '#fbecb1';
    }
    case 'end': {
      return '#f19386';
    }
    case 'process': {
      return '#cde6b2';
    }
    case 'start': {
      return '#b8d5eb';
    }
    default: {
      return '#f8fafc';
    }
  }
};

const getNodeStroke = () => {
  // 选中状态优先级最高
  if (props.selected) return '#4f46e5';

  // 高亮状态优先级其次
  if (props.highlighted) {
    switch (props.node.type) {
      case 'decision': {
        return '#f59e0b';
      } // 更深的黄色边框
      case 'end': {
        return '#ef4444';
      } // 更深的红色边框
      case 'process': {
        return '#6b7280';
      } // 更深的灰色边框
      case 'start': {
        return '#000';
      } // 更深的绿色边框
      default: {
        return '#6b7280';
      }
    }
  }

  // 悬停状态
  if (isHovered.value) return '#64748b';

  // 默认颜色
  switch (props.node.type) {
    case 'decision': {
      return '#d97706';
    }
    case 'end': {
      return '#dc2626';
    }
    case 'process': {
      return '#94a3b8';
    }
    case 'start': {
      return '#000';
    }
    default: {
      return '#94a3b8';
    }
  }
};

const getTextClass = () => {
  const classes = ['node-text'];
  if (props.node.type) classes.push(`text-${props.node.type}`);
  return classes.join(' ');
};

// 计算菱形的路径
const getDiamondPath = () => {
  const w = nodeWidth.value;
  const h = nodeHeight.value;
  return `M ${w / 2} 0 L ${w} ${h / 2} L ${w / 2} ${h} L 0 ${h / 2} Z`;
};

// 计算连接点位置
const getConnectionPoints = () => {
  const w = nodeWidth.value;
  const h = nodeHeight.value;

  switch (props.node.type) {
    case 'decision': {
      // 菱形的连接点在四个顶点
      return {
        top: { x: w / 2, y: 0 },
        bottom: { x: w / 2, y: h },
        left: { x: 0, y: h / 2 },
        right: { x: w, y: h / 2 },
      };
    }
    case 'end':
    case 'start': {
      // 圆形的连接点在上下
      const radius = Math.min(w, h) / 2;
      const centerX = w / 2;
      const centerY = h / 2;
      return {
        top: { x: centerX, y: centerY - radius },
        bottom: { x: centerX, y: centerY + radius },
      };
    }
    default: {
      // 矩形的连接点在上下
      return {
        top: { x: w / 2, y: 0 },
        bottom: { x: w / 2, y: h },
      };
    }
  }
};

const connectionPoints = computed(() => getConnectionPoints());
</script>

<template>
  <g
    class="flow-node"
    :transform="`translate(${node.x || 0}, ${node.y || 0})`"
    @click="handleClick"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- 不同类型的节点形状 -->

    <!-- 开始和结束节点 - 圆形 -->
    <circle
      v-if="node.type === 'start' || node.type === 'end'"
      :cx="centerX"
      :cy="centerY"
      :r="Math.min(nodeWidth, nodeHeight) / 2"
      :class="getNodeClass()"
      :fill="getNodeFill()"
      :stroke="getNodeStroke()"
      stroke-width="2"
    />

    <!-- 决策节点 - 菱形 -->
    <path
      v-else-if="node.type === 'decision'"
      :d="getDiamondPath()"
      :class="getNodeClass()"
      :fill="getNodeFill()"
      :stroke="getNodeStroke()"
      stroke-width="2"
    />

    <!-- 处理节点和其他 - 矩形 -->
    <rect
      v-else
      :width="nodeWidth"
      :height="nodeHeight"
      :rx="getNodeRadius()"
      :ry="getNodeRadius()"
      :class="getNodeClass()"
      :fill="getNodeFill()"
      :stroke="getNodeStroke()"
      stroke-width="2"
    />

    <!-- 节点文本 -->
    <text
      :x="centerX"
      :y="centerY"
      text-anchor="middle"
      dominant-baseline="middle"
      :class="getTextClass()"
      :font-size="18"
      fill="currentColor"
    >
      {{ node.label }}
    </text>

    <!-- 连接点 -->
    <template v-if="showConnectionPoints">
      <!-- 上连接点 -->
      <circle
        v-if="connectionPoints.top"
        :cx="connectionPoints.top.x"
        :cy="connectionPoints.top.y"
        r="4"
        class="connection-point connection-point-top"
        fill="#4f46e5"
      />

      <!-- 下连接点 -->
      <circle
        v-if="connectionPoints.bottom"
        :cx="connectionPoints.bottom.x"
        :cy="connectionPoints.bottom.y"
        r="4"
        class="connection-point connection-point-bottom"
        fill="#4f46e5"
      />

      <!-- 左连接点（仅菱形） -->
      <circle
        v-if="connectionPoints.left && node.type === 'decision'"
        :cx="connectionPoints.left.x"
        :cy="connectionPoints.left.y"
        r="4"
        class="connection-point connection-point-left"
        fill="#4f46e5"
      />

      <!-- 右连接点（仅菱形） -->
      <circle
        v-if="connectionPoints.right && node.type === 'decision'"
        :cx="connectionPoints.right.x"
        :cy="connectionPoints.right.y"
        r="4"
        class="connection-point connection-point-right"
        fill="#4f46e5"
      />
    </template>
  </g>
</template>

<style scoped>
@keyframes highlight-pulse {
  0%,
  100% {
    filter: drop-shadow(0 3px 8px rgb(59 130 246 / 40%));
  }

  50% {
    filter: drop-shadow(0 4px 12px rgb(59 130 246 / 60%));
  }
}

.flow-node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.node-shape {
  transition: all 0.2s ease;
}

.node-selected {
  filter: drop-shadow(0 4px 6px rgb(79 70 229 / 30%));
}

.node-highlighted {
  filter: drop-shadow(0 3px 8px rgb(59 130 246 / 40%));
  animation: highlight-pulse 2s ease-in-out infinite;
}

.node-hovered {
  filter: drop-shadow(0 2px 4px rgb(0 0 0 / 10%));
}

.node-text {
  font-weight: 500;
  pointer-events: none;
  user-select: none;
}

.connection-point {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.flow-node:hover .connection-point {
  opacity: 1;
}

/* 不同节点类型的特殊样式 */
.node-start,
.node-end {
  /* 椭圆形节点的特殊样式 */
}

.node-decision {
  /* 菱形节点的特殊样式 */
}

.node-process {
  /* 矩形节点的特殊样式 */
}
</style>
