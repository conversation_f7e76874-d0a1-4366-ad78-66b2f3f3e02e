<script setup lang="ts">
import type {
  InstanceListItemViewModel,
  ProcessInstanceSearcher,
} from '@coder/swf-api';

import { ref } from 'vue';

import { useTable } from '@vben/hooks';

import { BugOutlined } from '@ant-design/icons-vue';
import {
  createProcsssInstanceApi,
  getProcessInstanceStatus,
} from '@coder/swf-api';
import { swfOption } from '@coder/swf-render';
import {
  Button,
  Divider,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Modal,
  Switch,
  Table,
} from 'ant-design-vue';

import { SearchIcon } from '../../icon';
import { useDebugger } from '../hooks/useDebugger';
import { useWorkProcessStore } from '../stores';

const props = defineProps<{
  storeId: string;
}>();
const api = createProcsssInstanceApi(swfOption.request, swfOption.host);
const { attachProcessInstance } = useDebugger(props.storeId);
// const { onInit } = useDesigner(props.storeId);
const store = useWorkProcessStore(props.storeId);
const searchForm = ref<ProcessInstanceSearcher>({
  page: 1,
  pageSize: 50,
  isDebug: true,
  subject: '',
  workProcessId: store.workProcessId,
});

const { dataSource, pagination, onSearch } = useTable({
  fetchData: async () => {
    const data = await api.list(searchForm.value);
    const total = await api.count(searchForm.value);
    return {
      data,
      total,
    };
  },
  getPageInfo: () => {
    return {
      page: searchForm.value.page,
      pageSize: searchForm.value.pageSize,
    };
  },
});

const processInstanceId = ref(0);
const visible = ref(false);
const onDebugger = () => {
  visible.value = true;
};
const onStart = () => {
  attachProcessInstance(processInstanceId.value).then((resp) => {
    if (resp.success) {
      visible.value = false;
      message.info({ content: resp.message });
    } else {
      Modal.error({ content: resp.message });
    }
  });
};
const columns = ref([
  // { title: '实例Id', dataIndex: 'id', width: 50 },
  { title: '主题', dataIndex: 'subject', width: 200 },
  { title: '创建人', dataIndex: 'creatorName', width: 100 },
  { title: '工单号', dataIndex: 'number', width: 100 },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }: { record: InstanceListItemViewModel }) => {
      return getProcessInstanceStatus(record.status);
    },
  },
]);
const onSelect = (proceess: InstanceListItemViewModel) => {
  processInstanceId.value = proceess.id;
};
</script>
<template>
  <Button @click="onDebugger">
    <template #icon>
      <BugOutlined />
    </template>
  </Button>
  <Modal v-model:open="visible" title="选择调试工作流实例" width="700px">
    <Form layout="inline">
      <FormItem label="调试实例Id">
        <InputNumber v-model:value="processInstanceId" :min="1" />
      </FormItem>
    </Form>
    <Divider />
    <Form layout="inline">
      <FormItem label="主题">
        <Input v-model:value="searchForm.subject" />
      </FormItem>
      <FormItem label="工单号">
        <Input v-model:value="searchForm.number" />
      </FormItem>
      <FormItem label="是否debug">
        <Switch v-model:checked="searchForm.isDebug" />
      </FormItem>
      <FormItem>
        <Button @click="onSearch"><SearchIcon /></Button>
      </FormItem>
    </Form>
    <Table
      style="margin-top: 15px"
      bordered
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :scroll="{ y: 300 }"
      size="small"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'subject'">
          <a @click="onSelect(record as InstanceListItemViewModel)">{{
            record.subject
          }}</a>
        </template>
      </template>
    </Table>
    <template #footer>
      <Button type="primary" @click="onStart">确认</Button>
    </template>
  </Modal>
</template>
