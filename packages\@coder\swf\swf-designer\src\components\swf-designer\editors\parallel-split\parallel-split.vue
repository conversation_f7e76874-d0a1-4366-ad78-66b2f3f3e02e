<script setup lang="ts">
import type { ParallelSplitNodeSubmit } from '@coder/swf-api';

import { computed, ref } from 'vue';

import {
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { useSwfDesigner } from '../../hooks/useDesigner';
import nodeLink from '../_link.vue';

const props = defineProps<{
  storeId: string;
  value: ParallelSplitNodeSubmit;
}>();

const parallelSplit = computed(() => props.value);
const activeKey = ref('basic');

const { nodeNameChange } = useSwfDesigner(props.storeId);

// 处理节点名称变化
let oldName = parallelSplit.value.name;
const onNameChange = () => {
  nodeNameChange(oldName, parallelSplit.value.name);
  oldName = parallelSplit.value.name;
};

// 注释
const comment = computed({
  get: () => parallelSplit.value.comment || '',
  set: (value: string) => {
    parallelSplit.value.comment = value;
  },
});

// 下一个节点列表
const nextNodeNames = computed({
  get: () => parallelSplit.value.nextNodeNames || [],
  set: (value: string[]) => {
    parallelSplit.value.nextNodeNames = value;
  },
});

// 添加下一个节点
const addNextNode = () => {
  const newNodeName = `并行任务${nextNodeNames.value.length + 1}`;
  nextNodeNames.value = [...nextNodeNames.value, newNodeName];
};

// 移除下一个节点
const removeNextNode = (index: number) => {
  nextNodeNames.value = nextNodeNames.value.filter((_, i) => i !== index);
};

defineExpose({
  set(v: string) {
    activeKey.value = v === 'basic' ? 'basic' : 'outputs';
  },
});
</script>

<template>
  <Tabs v-model:active-key="activeKey" tab-position="left" type="card">
    <TabPane key="basic" tab="基本">
      <AForm
        v-if="parallelSplit"
        :label-col="{ span: 6 }"
        :model="parallelSplit"
        :wrapper-col="{ span: 18 }"
      >
        <AFormItem label="名称">
          <AInput
            v-model:value="parallelSplit.name"
            autocomplete="off"
            placeholder="同步任务名称"
            @blur="onNameChange"
          />
        </AFormItem>

        <AFormItem label="注释">
          <AInput.TextArea
            v-model:value="comment"
            placeholder="请输入任务注释"
            :rows="3"
          />
        </AFormItem>
      </AForm>
    </TabPane>

    <TabPane key="outputs" tab="输出节点">
      <div class="p-4">
        <h3>并行输出节点</h3>
        <p class="mb-4 text-gray-500">配置并行执行的下一个节点</p>

        <div
          v-for="(_, index) in nextNodeNames"
          :key="index"
          class="mb-4 rounded border p-3"
        >
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600">节点 {{ index + 1 }}:</span>
            <nodeLink
              v-model:node-name="nextNodeNames[index]"
              :exclude-node="parallelSplit.name"
              :store-id="props.storeId"
              class="flex-1"
            />
            <a-button
              type="text"
              danger
              size="small"
              @click="removeNextNode(index)"
            >
              删除
            </a-button>
          </div>
        </div>

        <a-button type="dashed" block @click="addNextNode" class="mt-2">
          添加并行输出节点
        </a-button>

        <div
          v-if="nextNodeNames.length === 0"
          class="py-8 text-center text-gray-400"
        >
          暂无并行输出节点，请点击上方按钮添加
        </div>
      </div>
    </TabPane>
  </Tabs>
</template>
