<script lang="ts" setup>
import type { TagSubmit, WorkActivityViewModel } from '@coder/swf-api';

import type { PropType } from 'vue';

import { computed, onMounted, ref, watch } from 'vue';

import { SaveOutlined } from '@ant-design/icons-vue';
import { ClipButton } from '@coder/clip-button';
import {
  createWorkflowApi,
  Priority,
  WorkActivityStatus,
} from '@coder/swf-api';
import { Button as AButton } from 'ant-design-vue';

import { swfOption } from '../../../swfOption';
import prioritySelector from '../../prioritySelector/index.vue';
// import CoderSwfProcessInstanceTag from '../../processInstance/tags/index.vue';

const props = defineProps({
  workActivity: {
    default: null,
    type: Object as PropType<WorkActivityViewModel>,
  },
});

const emits = defineEmits(['save']);

// 扩展信息
const tags = ref<Array<TagSubmit>>([]);
const workActivity = computed(() => props.workActivity);
const subtitle = computed(() => {
  return workActivity.value?.workTaskName;
});

const title = computed(() => {
  return workActivity.value?.subject || workActivity.value?.workProcessName;
});

const onSave = () => {
  const data = {
    PI_priority: workActivity.value?.priority || (Priority.Normal as Number),
    PI_tags: tags.value,
  };
  emits('save', data);
};

const priorityChanged = (v: number) => {
  if (workActivity.value) workActivity.value.priority = v;
};

const reloadTag = () => {
  if (!workActivity.value) return;
  const processInstanceId = workActivity.value.processInstanceId;
  if (!processInstanceId) return;
  const workflowApi = createWorkflowApi(swfOption.request, swfOption.host);
  workflowApi.getTags(processInstanceId).then((resp) => {
    tags.value.splice(0);
    tags.value.push(...resp);
  });
};
watch(
  () => props.workActivity,
  () => {
    reloadTag();
  },
);
onMounted(() => {
  reloadTag();
});
</script>

<template>
  <div class="modern-page-header">
    <!-- 主标题区域 -->
    <div class="header-main">
      <div class="title-section">
        <h1 class="main-title">{{ title }}</h1>
        <p v-if="subtitle" class="sub-title">{{ subtitle }}</p>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <AButton
          v-if="
            workActivity &&
            workActivity.status === WorkActivityStatus.Processing
          "
          size="small"
          type="primary"
          class="save-button"
          @click="() => onSave()"
        >
          <SaveOutlined /> 保存
        </AButton>
      </div>
    </div>

    <!-- 信息卡片区域 -->
    <div class="info-card">
      <div class="info-grid">
        <div class="info-item">
          <label class="info-label">创建者</label>
          <span class="info-value">{{
            workActivity?.processInstanceUserName || '-'
          }}</span>
        </div>

        <div class="info-item">
          <label class="info-label">工单号</label>
          <div class="info-value">
            <ClipButton
              v-if="workActivity"
              :value="workActivity?.number"
              size="small"
              class="clip-button"
            >
              {{ workActivity?.number }}
            </ClipButton>
          </div>
        </div>

        <div class="info-item priority-selector-item">
          <label class="info-label">优先级</label>
          <div class="info-value priority-selector-wrapper">
            <priority-selector
              v-if="workActivity"
              :priority="workActivity?.priority"
              @changed="priorityChanged"
              class="priority-selector"
            />
          </div>
        </div>

        <!-- 标签信息（当前隐藏） -->
        <div v-if="false" class="info-item info-item-full">
          <label class="info-label">标签</label>
          <div class="info-value">{{ tags }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-main {
    padding: 20px 20px 16px;
  }

  .info-card {
    padding: 16px 20px;
  }

  .main-title {
    font-size: 22px;
  }

  .info-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .header-main {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 20px 16px 16px;
  }

  .action-section {
    align-self: flex-start;
    margin-left: 0;
  }

  .main-title {
    font-size: 20px;
  }

  .sub-title {
    font-size: 13px;
  }

  .info-card {
    padding: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-item {
    gap: 8px;
  }

  .save-button {
    width: 100%;
    height: 44px;
  }
}

@media (max-width: 480px) {
  .header-main {
    padding: 16px 12px 12px;
  }

  .info-card {
    padding: 12px;
  }

  .main-title {
    font-size: 18px;
  }

  .sub-title {
    font-size: 12px;
  }

  .info-grid {
    gap: 12px;
  }

  .info-label {
    display: none !important;
  }

  .action-section {
    display: none !important;
  }

  .priority-selector-item {
    display: none !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-page-header {
    background: #1e293b;
    border-color: #334155;
  }

  .header-main {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    border-bottom-color: #334155;
  }

  .main-title {
    color: #f1f5f9;
  }

  .sub-title {
    color: #94a3b8;
  }

  .info-card {
    background: #1e293b;
  }

  .info-label {
    color: #94a3b8;
  }

  .info-value {
    color: #f1f5f9;
  }

  .clip-button {
    color: #cbd5e1;
    background: #334155;
    border-color: #475569;
  }

  .clip-button:hover {
    color: #e2e8f0;
    background: #475569;
    border-color: #64748b;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .modern-page-header,
  .save-button,
  .clip-button {
    transition: none;
    animation: none;
  }

  .modern-page-header:hover {
    transform: none;
  }

  .save-button:hover {
    transform: none;
  }
}

.modern-page-header {
  overflow: hidden;
  background: #fff;
  border: 1px solid #f1f5f9;
  border-radius: 12px;
  box-shadow:
    0 1px 3px 0 rgb(0 0 0 / 10%),
    0 1px 2px -1px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
  animation: fade-in-up 0.4s ease-out;
}

.modern-page-header:hover {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 10%),
    0 2px 4px -2px rgb(0 0 0 / 10%);
  transform: translateY(-1px);
}

/* 主标题区域 */
.header-main {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 24px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #fff 100%);
  border-bottom: 1px solid #f1f5f9;
}

.title-section {
  flex: 1;
  min-width: 0;
}

.main-title {
  margin: 0 0 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
  color: #1e293b;
  white-space: nowrap;
}

.sub-title {
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  line-height: 1.4;
  color: #64748b;
  white-space: nowrap;
}

.action-section {
  flex-shrink: 0;
  margin-left: 16px;
}

.save-button {
  height: 40px;
  padding: 0 20px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.save-button:hover {
  box-shadow: 0 4px 12px rgb(59 130 246 / 30%);
  transform: translateY(-1px);
}

.save-button:active {
  transform: translateY(0);
}

/* 信息卡片区域 */
.info-card {
  padding: 20px 24px;
  background: #fff;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item-full {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  display: flex;
  align-items: center;
  min-height: 20px;
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.clip-button {
  padding: 4px 8px;
  font-family:
    'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New',
    monospace;
  font-size: 13px;
  color: #475569;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.clip-button:hover {
  color: #334155;
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* 优先级选择器样式 */
.priority-selector-wrapper {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.priority-selector {
  width: 100%;
  min-width: 120px;
}

/* 响应式设计 */
</style>
