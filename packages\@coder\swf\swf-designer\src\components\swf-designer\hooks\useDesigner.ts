import type {
  ConditionDecisionSubmit,
  NodeSubmit,
  ScriptDecisionSubmit,
} from '@coder/swf-api';

import type { DesignerPropsType } from '../types';

import { nextTick } from 'vue';

import { message } from 'ant-design-vue';
import { uniqueId } from 'lodash-es';

import {
  useDiagramStore,
  useErrorStore,
  useTabsStore,
  useWorkProcessStore,
} from '../stores';
import { useTreeNodeStore } from '../stores/treeNode';
import { useEditor } from './useEditor/useEditor';
import { useSwfInteliSense } from './useInsteliSense';

/**
 * 编辑器主要入口。其他组件应该优先采用这个hooks 提供的方法。
 * 初始化或获取designer的hooks方法。
 * @param propsOrStoreId 如果是string，那么获取公共方法。
 * @returns 函数以及状态值。
 */
export const useSwfDesigner = (propsOrStoreId: DesignerPropsType | string) => {
  const isInit = typeof propsOrStoreId !== 'string';
  /**
   * 是否初始化状态。如果输入的StoreId，那么就不是初始化状态。
   */
  const storeId = isInit ? uniqueId('swf-designer') : propsOrStoreId;
  const workProcessStore = isInit
    ? useWorkProcessStore(storeId, propsOrStoreId)
    : useWorkProcessStore(storeId);
  const tabsStore = useTabsStore(storeId);
  const errorsStore = useErrorStore(storeId);
  const diagramStore = useDiagramStore(storeId);
  const treeNodeStore = useTreeNodeStore(storeId);

  const createTask = () => {
    const result = workProcessStore.createWorkTask();

    // 更新树菜单
    treeNodeStore.addNode(result);
    // 更新图形菜单
    const diagramStore = useDiagramStore(storeId);
    diagramStore.build();
    return result;
  };
  const createScriptDecision = (): ScriptDecisionSubmit => {
    const result = workProcessStore.createScriptDecision();

    // 更新树菜单.
    const treeNodeStore = useTreeNodeStore(storeId);
    treeNodeStore.addNode(result);
    // 更新图形菜单
    const diagramStore = useDiagramStore(storeId);
    diagramStore.build();
    return result;
  };

  const createConditionDecision = (): ConditionDecisionSubmit => {
    const result = workProcessStore.createConditionDecision();

    // 更新树菜单.
    const treeNodeStore = useTreeNodeStore(storeId);
    treeNodeStore.addNode(result);
    // 更新图形菜单
    const diagramStore = useDiagramStore(storeId);
    diagramStore.build();
    return result;
  };
  /**
   * 通过节点名字获取节点。
   * @param swfNodeName 节点名字
   * @returns {NodeSubmit|undefined} 返回NodeSumit对象。
   */
  const findByName = (swfNodeName: string): NodeSubmit | undefined => {
    const node = workProcessStore.workProcess.nodes.find(
      (_) => _.name === swfNodeName,
    );
    return node;
  };
  /**
   *
   * @param name node名称。
   */
  const deleteNode = (name: string) => {
    const deleteNode = findByName(name);
    if (deleteNode) {
      workProcessStore.removeNode(deleteNode);
      treeNodeStore.removeNode(deleteNode);
      nextTick(() => {
        diagramStore.build();
      });
    } else {
      message.error({ content: `${name} 节点不存在。` });
    }
  };
  const selectNode = (node: NodeSubmit) => {
    const { addEditorByNode } = useEditor(storeId);
    addEditorByNode(node);
  };
  const selectNodeByText = (swfNodeName: string) => {
    const node = findByName(swfNodeName);
    if (node === undefined)
      throw new Error(`无法找到id为 ${swfNodeName} 节点。`);
    selectNode(node);
  };
  const setFormDataInteliSense = (formClassInteliSense: string) => {
    const intelisense = useSwfInteliSense().formClass(formClassInteliSense);
    workProcessStore.workProcess.formTypeScriptDefined = intelisense;
  }; /**
   * 创建一个并行连接节点
   * @returns {ParallelJoinNodeSubmit}
   */
  const createParallelJoin = () => {
    const result = workProcessStore.createParallelJoin();
    // 更新树菜单.
    const treeNodeStore = useTreeNodeStore(storeId);
    treeNodeStore.addNode(result);
    // 更新图形菜单
    const diagramStore = useDiagramStore(storeId);
    diagramStore.build();
    return result;
  };
  /**
   * 创建一个并行分割节点
   * @returns {ParallelSplitNodeSubmit}
   */
  const createParallelSplit = () => {
    const result = workProcessStore.createParallelSplit();
    // 更新树菜单.
    const treeNodeStore = useTreeNodeStore(storeId);
    treeNodeStore.addNode(result);
    // 更新图形菜单
    const diagramStore = useDiagramStore(storeId);
    diagramStore.build();
    return result;
  };
  return {
    createParallelJoin,
    createParallelSplit,
    createConditionDecision,
    createScriptDecision,
    createTask,
    deleteNode,
    nodeNameChange: (oldName: string, newName: string) => {
      /**
       * 当workTask/startNode/decision等 name改变的时候，
       * 由name创建的navigateKey都会变化。所以要调用以下方法
       * 一一改变。
       */
      workProcessStore.nodeNameChange(oldName, newName);
      // 更新图形菜单
      // 因为mermaid是全部重新创建。
      const diagramStore = useDiagramStore(storeId);
      diagramStore.build();

      const node = findByName(newName);

      if (node) {
        // 更新 treeNode.
        treeNodeStore.rebuildNavKey(node);
        // 更新tabs的key。
        tabsStore.rebuildNavKey(node);
      }
    },

    /**
     * 加载工作流以及相关重新刷新面板
     */
    onInit: () => {
      tabsStore.reset();
      errorsStore.reset();
      workProcessStore.reload().then((wp) => {
        diagramStore.build();
        treeNodeStore.build();
        const { addWorkProcess } = useEditor(storeId);
        addWorkProcess();
        if (wp.formManageDesign) setFormDataInteliSense(wp.formManageDesign);
      });
    },
    selectNode,
    selectNodeByText,
    setFormDataInteliSense,

    storeId,
  };
};
