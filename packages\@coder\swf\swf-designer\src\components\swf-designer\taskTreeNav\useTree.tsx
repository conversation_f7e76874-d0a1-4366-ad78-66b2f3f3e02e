/**
 * 工作流树导航组件
 *
 * 术语说明：
 * - swfNode: 指的是 INodeSubmit，即工作流中的 WorkTask/Decision 等节点
 * - treeNode/swfTreeNode: 树的节点，用于树导航显示的数据结构
 *
 * 该文件主要负责构建工作流的树形导航结构，包括：
 * - 工作任务节点
 * - 决策节点
 * - 并行分支/合并节点
 * - 工作流程事件节点
 */

// 导入工作流节点类型定义
import type {
  NodeSubmit,
  ParallelJoinNodeSubmit,
  ParallelSplitNodeSubmit,
  ScriptDecisionSubmit,
  WorkProcessSubmit,
  WorkTaskSubmit,
} from '@coder/swf-api';

// Vue 相关类型
import type { VNode } from 'vue';

// 导入事件总线类型定义
import type {
  workProcessEventNameType,
  workTaskEventNameType,
} from '../hooks/eventBus';
import type { NavigateKey } from '../types';

// 工作流类型枚举
import { workflowType } from '@coder/swf-api';

// 导入相关hooks和工具
import { workProcessEventName } from '../hooks/eventBus';
import { useEditor } from '../hooks/useEditor/useEditor';
import { useEditorNav } from '../hooks/useEditorNav';
import { useWorkProcessStore } from '../stores';
import { buildWorkTaskNode } from './_workTask';

/**
 * 工作流树节点类型枚举
 * 定义了树形导航中所有可能的节点类型
 */
// eslint-disable-next-line no-restricted-syntax
export const enum SwfNodeType {
  /** 工作流程节点 */
  workPrcess,
  /** 工作任务节点 */
  workTask,
  /** 决策节点 */
  decision,
  /** 命令节点 */
  command,
  /** 事件节点 */
  eventName,
  /** 决策脚本节点 */
  decisionScript,
  /** 开始节点 */
  start,
  /** 表单设计节点 */
  formDesign,
  /** 脚本分配节点 */
  assignByScript,
  /** 选择分配节点 */
  assignBySelect,
  /** 空节点 */
  none,
}

/**
 * 工作流树节点数据结构
 * 用于构建树形导航的节点对象
 */
export type SwfTreeNode = {
  /** 子节点数组 */
  children: Array<SwfTreeNode> | undefined;
  /** 决策节点数据（注意：原代码中的拼写，保持兼容性） */
  decison?: ScriptDecisionSubmit;
  /** 节点的唯一标识键 */
  key: NavigateKey;
  /** 开始节点数据 */
  startNode?: NodeSubmit;
  /** 节点显示标题，可以是字符串或返回VNode的函数 */
  title: string | { (): VNode };
  /** 节点类型 */
  type: SwfNodeType;
  /** 工作流程数据 */
  workProcess?: WorkProcessSubmit;
  /** 工作任务数据 */
  worktask?: WorkTaskSubmit;
};

/**
 * 创建基础树节点
 * @param swfNode - 工作流节点数据
 * @param key - 节点唯一标识
 * @param buildChildren - 是否构建子节点数组
 * @returns 树节点对象
 */
export const createTreeNode = (
  swfNode: NodeSubmit,
  key: string,
  buildChildren: boolean,
): SwfTreeNode => {
  return {
    key,
    title: () => <span title={key}>{swfNode.name}</span>,
    type: SwfNodeType.workTask,
    children: buildChildren ? new Array<SwfTreeNode>() : undefined,
  } as SwfTreeNode;
};

/**
 * 构建决策树节点
 * 决策节点包含一个脚本子节点用于编辑决策逻辑
 * @param decision - 脚本决策节点数据
 * @returns 决策树节点
 */
const buildDecisionTreeNode = (decision: ScriptDecisionSubmit): SwfTreeNode => {
  const { builder } = useEditorNav();

  // 创建决策节点
  const decisionNode = createTreeNode(
    decision,
    builder.decision.bool.editor(decision),
    true,
  );

  // 设置决策数据（注意：保持原有的拼写以兼容现有代码）
  decisionNode.decison = decision;
  decisionNode.type = SwfNodeType.decision;

  // 添加脚本子节点
  decisionNode.children?.push({
    decison: decision,
    key: builder.decision.script(decision),
    title: '脚本',
    type: SwfNodeType.decisionScript,
  } as SwfTreeNode);

  return decisionNode;
};

/**
 * 构建并行合并节点
 * @param join - 并行合并节点数据
 * @returns 并行合并树节点
 */
const buildParallelJoinNode = (join: ParallelJoinNodeSubmit): SwfTreeNode => {
  const { builder } = useEditorNav();
  const result = createTreeNode(join, builder.parallel.join(join), false);
  result.key = builder.parallel.join(join);
  return result;
};

/**
 * 构建并行分割节点
 * @param split - 并行分割节点数据
 * @returns 并行分割树节点
 */
const buildParallelSplitNode = (
  split: ParallelSplitNodeSubmit,
): SwfTreeNode => {
  const { builder } = useEditorNav();
  const result = createTreeNode(split, builder.parallel.split(split), false);
  result.key = builder.parallel.split(split);
  return result;
};

/**
 * 构建工作流程的完整树结构
 *
 * 节点标识说明：
 * - '@' 表示事件节点
 * - '#' 表示命令节点
 * - 无前缀表示普通节点
 *
 * @param workProcess - 工作流程数据
 * @returns 完整的工作流程树节点
 */
const buildTree = (workProcess: WorkProcessSubmit): SwfTreeNode => {
  // 创建根节点（工作流程节点）
  const workProcessNode = {
    key: 'workProcess',
    title: workProcess.name,
    type: SwfNodeType.workPrcess,
    children: new Array<SwfTreeNode>(),
  } as SwfTreeNode;

  const { builder } = useEditorNav();

  /**
   * 创建事件节点的辅助函数
   * @param key - 节点键值
   * @param title - 节点标题
   * @returns 事件树节点
   */
  const createEventNode = (key: NavigateKey, title: string): SwfTreeNode => {
    return {
      key,
      title: () => {
        // 检查是否有对应的脚本代码
        let hasCode = false;
        switch (title) {
          case workProcessEventName.cancel: {
            hasCode = !!workProcess.onCancelScript?.length;
            break;
          }
          case workProcessEventName.complete: {
            hasCode = !!workProcess.onCompleteScript?.length;
            break;
          }
          case workProcessEventName.start: {
            hasCode = !!workProcess.onStartScript?.length;
            break;
          }
        }

        // 如果有脚本代码，显示红色星号标识
        return hasCode ? (
          <span>
            {title} <span style="color:red"> * </span>
          </span>
        ) : (
          <span>{title}</span>
        );
      },
      type: SwfNodeType.eventName,
      children: undefined,
    } as SwfTreeNode;
  };

  // 添加工作流程事件节点
  workProcessNode.children?.push(
    createEventNode(
      builder.workProcess.startCode(),
      workProcessEventName.start,
    ),
    createEventNode(
      builder.workProcess.completeCode(),
      workProcessEventName.complete,
    ),
    createEventNode(
      builder.workProcess.cancelCode(),
      workProcessEventName.cancel,
    ),
  );

  // 添加表单设计节点
  workProcessNode.children?.push({
    key: builder.workProcess.formDesign(),
    title: '表单设计',
    type: SwfNodeType.formDesign,
  } as SwfTreeNode);

  // 添加管理员表单节点
  workProcessNode.children?.push({
    key: builder.workProcess.manageFormDesign(),
    title: '管理员表单',
    type: SwfNodeType.formDesign,
  } as SwfTreeNode);

  // 遍历工作流程中的所有节点，构建对应的树节点
  workProcess.nodes.forEach((swfNode: NodeSubmit) => {
    let treeNode: null | SwfTreeNode = null;

    // 忽略结束节点，因为它们没有编辑界面
    if (swfNode.name === '结束') return;

    // 根据节点类型构建不同的树节点
    switch (swfNode.$type) {
      case workflowType.boolScriptDecision: {
        // 构建布尔脚本决策节点
        treeNode = buildDecisionTreeNode(swfNode as ScriptDecisionSubmit);
        break;
      }
      case workflowType.startNode: {
        // 构建开始节点
        treeNode = createTreeNode(swfNode, builder.start.editor(), false);
        treeNode.startNode = swfNode;
        treeNode.type = SwfNodeType.start;
        break;
      }
      case workflowType.workTask: {
        // 构建工作任务节点
        treeNode = buildWorkTaskNode(swfNode as WorkTaskSubmit);
        break;
      }
    }

    // 如果成功创建了树节点，则添加到工作流程节点的子节点中
    if (treeNode !== null) {
      workProcessNode.children?.push(treeNode);
    }
  });

  return workProcessNode;
};
/**
 * 树构建器Hook
 * 提供构建和操作工作流树的各种方法
 * @param storeId - 存储标识符
 * @returns 树构建器对象，包含各种构建和操作方法
 */
export const useTreeBuilder = (storeId: string) => {
  const { builder } = useEditorNav();

  return {
    /**
     * 构建完整的工作流程树
     * @returns 工作流程树根节点
     */
    buildTree: () => {
      const store = useWorkProcessStore(storeId);
      return buildTree(store.workProcess);
    },

    /**
     * 创建JavaScript决策节点
     * @param sc - 脚本决策数据
     * @returns 决策树节点
     */
    createJsDecision: (sc: ScriptDecisionSubmit) => {
      return buildDecisionTreeNode(sc);
    },

    /**
     * 创建工作任务节点
     * @param workTask - 工作任务数据
     * @returns 工作任务树节点
     */
    createWorkTask: (workTask: WorkTaskSubmit) => {
      return buildWorkTaskNode(workTask);
    },

    /**
     * 创建并行合并节点
     * @param join - 并行合并数据
     * @returns 并行合并树节点
     */
    createParallelJoin: (join: ParallelJoinNodeSubmit) => {
      return buildParallelJoinNode(join);
    },

    /**
     * 创建并行分割节点
     * @param split - 并行分割数据
     * @returns 并行分割树节点
     */
    createParallelSplit: (split: ParallelSplitNodeSubmit) => {
      return buildParallelSplitNode(split);
    },
    /**
     * 处理树节点选择事件
     * 根据不同的节点类型打开对应的编辑器
     * @param node - 被选中的树节点
     */
    onSelect: (node: SwfTreeNode) => {
      // 获取各种编辑器的添加方法
      const {
        addDecisionCodeEditor,
        addDecisionEditor: addScriptDecisionEditor,
        addStartEditor,
        addWorkProcessFormDesigner,
        addWorkProcessManageFormDesigner,
        addWorkProcessScript,
        addWorkTaskAssignerEditor,
        addWorkTaskEditor,
        addWorkTaskEventEditor,
        addWorkTaskFormDesign,
      } = useEditor(storeId);

      // 根据节点类型执行相应的操作
      switch (node.type) {
        case SwfNodeType.assignByScript: {
          // 脚本分配节点
          if (node.worktask === undefined) throw new Error('工作任务不能为空');
          addWorkTaskAssignerEditor(node.worktask);
          break;
        }
        case SwfNodeType.assignBySelect: {
          // 选择分配节点
          if (node.worktask === undefined) throw new Error('工作任务不能为空');
          addWorkTaskAssignerEditor(node.worktask);
          break;
        }
        case SwfNodeType.command:
        case SwfNodeType.eventName: {
          // 命令节点或事件节点
          const keyAry = node.key.split('/');
          const eventType = keyAry[keyAry.length - 1];
          if (node.worktask) {
            // 工作任务事件
            addWorkTaskEventEditor(
              node.worktask,
              eventType as workTaskEventNameType,
            );
          } else {
            // 工作流程事件
            addWorkProcessScript(eventType as workProcessEventNameType);
          }
          break;
        }
        case SwfNodeType.decision: {
          // 决策节点
          if (node.decison === undefined) throw new Error('决策节点不能为空');
          addScriptDecisionEditor(node.decison);
          break;
        }
        case SwfNodeType.decisionScript: {
          // 决策脚本节点
          if (node.decison === undefined) throw new Error('决策节点不能为空');
          addDecisionCodeEditor(node.decison);
          break;
        }
        case SwfNodeType.formDesign: {
          // 表单设计节点
          if (node.worktask) {
            // 工作任务表单设计
            addWorkTaskFormDesign(node.worktask);
          } else if (node.key === builder.workProcess.manageFormDesign()) {
            // 管理员表单设计
            addWorkProcessManageFormDesigner();
          } else {
            // 普通表单设计
            addWorkProcessFormDesigner();
          }
          break;
        }
        case SwfNodeType.none: {
          // 空节点，无操作
          break;
        }
        case SwfNodeType.start: {
          // 开始节点
          addStartEditor();
          break;
        }
        case SwfNodeType.workTask: {
          // 工作任务节点
          if (node.worktask === undefined) throw new Error('工作任务不能为空');
          addWorkTaskEditor(node.worktask);
          break;
        }
      }
    },
  };
};
