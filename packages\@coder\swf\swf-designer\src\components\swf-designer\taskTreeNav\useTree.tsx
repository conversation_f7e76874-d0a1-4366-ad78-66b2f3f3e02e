/**
 * swfNode 指的是 INodeSubmit 就是工作流中的 WorkTask/Decision
 * treeNode /swfTreeNode 就是树的节点，是 所有通过 树导航的
 *
 */

import type {
  NodeSubmit,
  ParallelJoinNodeSubmit,
  ParallelSplitNodeSubmit,
  ScriptDecisionSubmit,
  WorkProcessSubmit,
  WorkTaskSubmit,
} from '@coder/swf-api';

import type { VNode } from 'vue';

import type {
  workProcessEventNameType,
  workTaskEventNameType,
} from '../hooks/eventBus';
import type { NavigateKey } from '../types';

import { workflowType } from '@coder/swf-api';

import { workProcessEventName } from '../hooks/eventBus';
import { useEditor } from '../hooks/useEditor/useEditor';
import { useEditorNav } from '../hooks/useEditorNav';
import { useWorkProcessStore } from '../stores';
import { buildWorkTaskNode } from './_workTask';

// eslint-disable-next-line no-restricted-syntax
export const enum SwfNodeType {
  workPrcess,
  workTask,
  decision,
  command,
  /**
   * 事件
   */
  eventName,
  decisionScript,
  start,
  formDesign,
  assignByScript,
  assignBySelect,
  none,
}
export type SwfTreeNode = {
  children: Array<SwfTreeNode> | undefined;
  decison?: ScriptDecisionSubmit;
  key: NavigateKey;
  startNode?: NodeSubmit;
  title: string | { (): VNode };
  type: SwfNodeType;
  workProcess?: WorkProcessSubmit;
  worktask?: WorkTaskSubmit;
};

export const createTreeNode = (
  swfNode: NodeSubmit,
  key: string,
  buildChildren: boolean,
): SwfTreeNode => {
  return {
    key,
    title: () => <span title={key}>{swfNode.name}</span>,
    type: SwfNodeType.workTask,
    children: buildChildren ? new Array<SwfNodeType>() : undefined,
  } as SwfTreeNode;
};

const buildDecisionTreeNode = (decision: ScriptDecisionSubmit): SwfTreeNode => {
  const { builder } = useEditorNav();

  const decisionNode = createTreeNode(
    decision,
    builder.decision.bool.editor(decision),
    true,
  );

  decisionNode.decison = decision;

  decisionNode.type = SwfNodeType.decision;
  decisionNode.children?.push({
    decison: decision,
    key: builder.decision.script(decision),
    title: '脚本',
    type: SwfNodeType.decisionScript,
  } as SwfTreeNode);

  return decisionNode;
};

const buildParallelJoinNode = (join: ParallelJoinNodeSubmit): SwfTreeNode => {
  const { builder } = useEditorNav();
  const result = createTreeNode(join, builder.parallel.join(join), false);
  result.key = builder.parallel.join(join);
  return result;
};
const buildParallelSplitNode = (join: ParallelSplitNodeSubmit): SwfTreeNode => {
  const { builder } = useEditorNav();
  const result = createTreeNode(join, builder.parallel.split(join), false);
  result.key = builder.parallel.split(join);
  return result;
};

/**
 * 创建有关WorkProcess的树模型。
 * 其中'@' 是事件，# 是命令，什么都没有就是普通的
 * @param workPrcess
 * @returns 返回treenode
 */
const buildTree = (workPrcess: WorkProcessSubmit): SwfTreeNode => {
  const workProcessNode = {
    key: 'workProcess',
    title: workPrcess.name,
    type: SwfNodeType.workPrcess,
    children: new Array<SwfTreeNode>(),
  } as SwfTreeNode;

  const { builder } = useEditorNav();
  const _c = (key: NavigateKey, title: string) => {
    return {
      key,
      title: () => {
        let hasCode = false;
        switch (title) {
          case workProcessEventName.cancel: {
            hasCode = !!workPrcess.onCancelScript?.length;
            break;
          }
          case workProcessEventName.complete: {
            hasCode = !!workPrcess.onCompleteScript?.length;
            break;
          }
          case workProcessEventName.start: {
            hasCode = !!workPrcess.onStartScript?.length;
            break;
          }
        }
        return hasCode ? (
          <span>
            {title} <span style="color:red"> * </span>
          </span>
        ) : (
          <span>{title}</span>
        );
      },
      type: SwfNodeType.eventName,
      children: undefined,
    } as SwfTreeNode;
  };
  workProcessNode.children?.push(
    _c(builder.workProcess.startCode(), workProcessEventName.start),
    _c(builder.workProcess.completeCode(), workProcessEventName.complete),
    _c(builder.workProcess.cancelCode(), workProcessEventName.cancel),
  );

  // 设计器

  workProcessNode.children?.push({
    key: builder.workProcess.formDesign(),
    title: '表单设计',
    type: SwfNodeType.formDesign,
  } as SwfTreeNode);

  workProcessNode.children?.push({
    key: builder.workProcess.manageFormDesign(),
    title: '管理员表单',
    type: SwfNodeType.formDesign,
  } as SwfTreeNode);

  workPrcess.nodes.forEach((swfNode) => {
    let treeNode: null | SwfTreeNode = null;

    // 忽略结束，因为他们没有时间
    if (swfNode.name === '结束') return;

    switch (swfNode.$type) {
      case workflowType.boolScriptDecision: {
        treeNode = buildDecisionTreeNode(swfNode as ScriptDecisionSubmit);
        break;
      }
      case workflowType.startNode: {
        treeNode = createTreeNode(swfNode, builder.start.editor(), false);
        treeNode.startNode = swfNode;
        treeNode.type = SwfNodeType.start;
        break;
      }
      case workflowType.workTask: {
        treeNode = buildWorkTaskNode(swfNode as WorkTaskSubmit);
        break;
      }
    }
    if (treeNode !== null) {
      workProcessNode.children?.push(treeNode);
      treeNode = null;
    }
  });

  return workProcessNode;
};
export const useTreeBuilder = (storeId: string) => {
  const { builder } = useEditorNav();
  return {
    buildTree: () => {
      const store = useWorkProcessStore(storeId);
      return buildTree(store.workProcess);
    },
    createJsDecision: (sc: ScriptDecisionSubmit) => {
      return buildDecisionTreeNode(sc);
    },
    createWorkTask: (workTask: WorkTaskSubmit) => {
      return buildWorkTaskNode(workTask);
    },
    createParallelJoin: (join: ParallelJoinNodeSubmit) => {
      return buildParallelJoinNode(join);
    },
    createParallelSplit: (split: ParallelSplitNodeSubmit) => {
      return buildParallelSplitNode(split);
    },
    onSelect: (node: SwfTreeNode) => {
      const {
        addDecisionCodeEditor,
        addDecisionEditor: addScriptDecisionEditor,
        addStartEditor,
        addWorkProcessFormDesigner,
        addWorkProcessManageFormDesigner,
        addWorkProcessScript,
        addWorkTaskAssignerEditor,
        addWorkTaskEditor,
        addWorkTaskEventEditor,
        addWorkTaskFormDesign,
      } = useEditor(storeId);

      switch (node.type) {
        case SwfNodeType.assignByScript: {
          if (node.worktask === undefined)
            throw new Error('worktask can not be null');
          addWorkTaskAssignerEditor(node.worktask);
          break;
        }
        case SwfNodeType.assignBySelect: {
          if (node.worktask === undefined)
            throw new Error('worktask can not be null');
          addWorkTaskAssignerEditor(node.worktask);
          break;
        }

        case SwfNodeType.command:
        case SwfNodeType.eventName: {
          const keyAry = node.key.split('/');
          const eventType = keyAry[keyAry.length - 1];
          if (node.worktask) {
            addWorkTaskEventEditor(
              node.worktask,
              eventType as workTaskEventNameType,
            );
          } else {
            addWorkProcessScript(eventType as workProcessEventNameType);
          }
          break;
        }
        case SwfNodeType.decision: {
          if (node.decison === undefined)
            throw new Error('decison can not be null');
          addScriptDecisionEditor(node.decison);
          break;
        }
        case SwfNodeType.decisionScript: {
          if (node.decison === undefined)
            throw new Error('decison can not be null');
          addDecisionCodeEditor(node.decison);
          break;
        }
        case SwfNodeType.formDesign: {
          if (node.worktask) addWorkTaskFormDesign(node.worktask);
          else if (node.key === builder.workProcess.manageFormDesign())
            addWorkProcessManageFormDesigner();
          else addWorkProcessFormDesigner();
          break;
        }
        case SwfNodeType.none: {
          break;
        }
        case SwfNodeType.start: {
          addStartEditor();
          break;
        }
        case SwfNodeType.workTask: {
          if (node.worktask === undefined)
            throw new Error('worktask can not be null');
          addWorkTaskEditor(node.worktask);
          break;
        }
      }
    },
  };
};
