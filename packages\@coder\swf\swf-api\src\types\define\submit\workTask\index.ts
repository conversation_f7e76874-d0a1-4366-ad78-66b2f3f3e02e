import type { NextTaskPerformers } from '../../../enum';
import type { SingleOutputNodeSubmit } from '../NodeSubmit';
import type { ScriptAssignerSubmit, UserAssigner } from './assigners';

export * from './assigners';
export type WorkTaskScriptSubmit = {
  script: string;
};
export type WorkTaskCommandSubmit = {
  $type: string;
  id: number;
  name: string;
  order: number;
};

export type ScriptCommandSubmit = WorkTaskCommandSubmit & {
  script: string;
};

export type WorkTaskSubmit = {
  assigner: ScriptAssignerSubmit | UserAssigner;

  auto: boolean;

  canGiveUp: boolean;

  commands: Array<WorkTaskCommandSubmit>;

  formDesign: string;
  nextTaskPerformers: NextTaskPerformers;
  suggestionComment: string;
  /** wokActivity结束 */
  workActivityCompleteScript?: WorkTaskScriptSubmit;

  /** 工作任务结束 */
  workTaskCompleteScript?: WorkTaskScriptSubmit;

  /**
   * 工作任务开始
   */
  workTaskStartScript?: WorkTaskScriptSubmit;
} & SingleOutputNodeSubmit;
