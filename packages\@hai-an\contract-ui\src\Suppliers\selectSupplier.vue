<script setup lang="ts">
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';

import { computed, onMounted, reactive, ref } from 'vue';

import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  createSupplierApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as AButton,
  InputGroup as AInputGroup,
  Modal as AModal,
  Select as ASelect,
  Tooltip as ATooltip,
} from 'ant-design-vue';

import ContractEdit from './components/edit.vue';

const props = defineProps({
  modelValue: { default: () => '', type: String },
});
const emit = defineEmits(['update:modelValue', 'change']);

const api = createSupplierApi(options.request, options.path);

const isEdit = ref(false);
const editId = ref(0);
const loading = ref(false);
const searchForm = reactive({
  isDeleted: false,
  page: 1,
  pageSize: 50,
  name: '',
});

const supplierInfo = computed({
  get: () => {
    return props.modelValue;
  },
  set: (v) => {
    emit('update:modelValue', v);
  },
});
const datas = reactive<DefaultOptionType[]>([]);

const handleChange = (val: SelectValue) => {
  const optionItem = datas.find((item) => item.value === val);
  if (optionItem) emit('change', optionItem.supplier);
};
const toSearch = () => {
  loading.value = true;
  api.list(searchForm).then((res) => {
    datas.splice(0);

    res.forEach((supp) => {
      datas.push({
        value: `${supp.name}(${supp.code})`,
        label: `${supp.name}(${supp.code})`,
        supplier: supp,
      });
    });

    loading.value = false;
  });
};
const handleSearch = (val: any) => {
  searchForm.name = val;
  toSearch();
};
const onAdd = () => {
  isEdit.value = true;
  editId.value = 0;
};

const canceled = () => {
  isEdit.value = false;
};

const saved = async () => {
  canceled();
  const val = await ContractEditRef.value?.save();
  if (!val) return;
  searchForm.name = val.name;
  const matchValue = `${val.name}(${val.code})`;
  emit('update:modelValue', matchValue);

  datas.push({
    value: matchValue,
    label: matchValue,
    supplier: val,
  });
  handleChange(matchValue);
};

onMounted(() => {
  supplierInfo.value = props.modelValue;
  toSearch();
});

const ContractEditRef = ref<InstanceType<typeof ContractEdit>>();
</script>
<template>
  <div>
    <AInputGroup compact>
      <ASelect
        v-model:value="supplierInfo"
        :default-active-first-option="false"
        :filter-option="false"
        :loading="loading"
        :not-found-content="null"
        :options="datas"
        style="width: 100%; min-width: 100px"
        placeholder="请输入公司名称"
        show-search
        @change="(api, b) => handleChange(api, b)"
        @search="handleSearch"
        allow-clear
      >
        <template #suffixIcon>
          <SearchOutlined />
        </template>
      </ASelect>
      <ATooltip title="新增对方单位">
        <AButton type="link" @click="onAdd">
          <PlusOutlined />
        </AButton>
      </ATooltip>
    </AInputGroup>
    <AModal
      :destroy-on-close="true"
      :mask-closable="false"
      :open="isEdit"
      :width="1000"
      title="新增对方单位"
      @cancel="canceled"
    >
      <ContractEdit :id="editId" ref="ContractEditRef" />
      <template #footer>
        <AButton type="primary" @click="canceled">取消</AButton>
        <AButton type="primary" @click="saved">保存</AButton>
      </template>
    </AModal>
  </div>
</template>
