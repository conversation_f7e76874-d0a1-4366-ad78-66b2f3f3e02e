# 并行节点实现文档

## 概述

本文档描述了在 FlowNode 组件中新增的两种并行节点类型：`parallelSplit`（并行分割）和 `parallelJoin`（并行合并）。

## 节点类型

### 1. parallelSplit（并行分割节点）

**用途：** 将单一流程分割为多个并行分支

**视觉设计：**
- 基础形状：矩形（与 process 节点相同）
- 特殊标识：四个角有三角形指向中心，表示收集
- 颜色：橙色系（#fed7aa 填充，#f97316 边框）
- 连接点：上下左右四个标准连接点

**使用示例：**
```javascript
{
  id: 'split1',
  label: '并行分割',
  type: 'parallelSplit',
  x: 100,
  y: 100,
  width: 160,
  height: 90
}
```

### 2. parallelJoin（并行合并节点）

**用途：** 将多个并行分支合并为单一流程

**视觉设计：**
- 基础形状：矩形（与 process 节点相同）
- 特殊标识：四个角有三角形指向外面，表示分散
- 颜色：紫色系（#ddd6fe 填充，#a855f7 边框）
- 连接点：上下左右四个标准连接点

**使用示例：**
```javascript
{
  id: 'join1',
  label: '并行合并',
  type: 'parallelJoin',
  x: 300,
  y: 100,
  width: 160,
  height: 90
}
```

## 状态样式

### 默认状态
- parallelSplit: 浅橙色填充，橙色边框
- parallelJoin: 浅紫色填充，紫色边框

### 悬停状态
- 填充色变为浅灰色 (#f1f5f9)
- 边框色变为深灰色 (#64748b)
- 添加阴影效果

### 选中状态
- 填充色变为浅蓝色 (#e0e7ff)
- 边框色变为蓝色 (#4f46e5)
- 添加蓝色阴影

### 高亮状态
- parallelSplit: 深橙色填充和边框
- parallelJoin: 深紫色填充和边框
- 脉冲动画效果

## 连接点

### parallelSplit 和 parallelJoin 连接点
两种并行节点都使用标准的四个连接点：
- `top`: 顶部中心连接点
- `bottom`: 底部中心连接点
- `left`: 左侧中心连接点
- `right`: 右侧中心连接点

这种设计与决策节点保持一致，便于在工作流中灵活连接。

## 技术实现

### SVG 组合绘制
并行节点使用组合方式绘制：
1. **基础矩形**: 使用标准的 `<rect>` 元素
2. **三角形标识**: 使用 `<path>` 元素绘制四个三角形
   - `getParallelSplitTriangles()`: 生成向外指向的三角形
   - `getParallelJoinTriangles()`: 生成向内指向的三角形

### 连接点计算
在 `getConnectionPoints()` 函数中，并行节点使用与决策节点相同的四点连接模式。

### 样式系统
- 颜色通过 `getNodeFill()` 和 `getNodeStroke()` 函数管理
- CSS 类通过 `getNodeClass()` 函数生成
- 三角形使用边框色填充，确保视觉一致性
- 支持所有标准状态：默认、悬停、选中、高亮

## 使用建议

1. **尺寸建议：** 建议使用 160x90 或更大的尺寸以确保图形清晰可见
2. **布局建议：** 在工作流中，分割节点通常在合并节点之前
3. **连接建议：** 使用连接点进行精确的线条连接
4. **可访问性：** 确保节点标签清晰描述其功能

## 测试

可以使用 `test-parallel-nodes.vue` 文件查看所有节点类型的对比效果和不同状态的显示。

## 兼容性

- 完全兼容现有的 FlowNode 组件 API
- 支持所有现有的属性和事件
- 与其他节点类型保持一致的交互行为
