<script setup lang="ts">
import type { ParallelJoinNodeSubmit } from '@coder/swf-api';

import { computed, ref } from 'vue';

import { CodeEditor } from '@coder/code-editor';
import { JoinConditionSubmit } from '@coder/swf-api';
import {
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Select as ASelect,
  SelectOption as ASelectOption,
  Checkbox,
  CheckboxGroup,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { useSwfDesigner } from '../../hooks/useDesigner';
import {
  IntellisensType,
  useSwfInteliSense,
} from '../../hooks/useInsteliSense';

const props = defineProps<{
  storeId: string;
  value: ParallelJoinNodeSubmit;
}>();

const parallelJoin = computed(() => props.value);
const activeKey = ref('basic');

const { nodeNameChange } = useSwfDesigner(props.storeId);

const SetIntellisence = (intell: IntellisensType) => {
  useSwfInteliSense().setIntell(intell);
};

// 处理节点名称变化
let oldName = parallelJoin.value.name;
const onNameChange = () => {
  nodeNameChange(oldName, parallelJoin.value.name);
  oldName = parallelJoin.value.name;
};

// 等待任务列表
const waitForWorkTasks = computed({
  get: () => parallelJoin.value.waitForWorkTasks || [],
  set: (value: string[]) => {
    parallelJoin.value.waitForWorkTasks = value;
  },
});

// 自定义汇聚脚本
const customJoinScript = computed({
  get: () => parallelJoin.value.customJoinScript || '',
  set: (value: string) => {
    parallelJoin.value.customJoinScript = value;
  },
});

// 汇聚条件
const joinCondition = computed({
  get: () => parallelJoin.value.joinCondition || JoinConditionSubmit.All,
  set: (value: JoinConditionSubmit) => {
    parallelJoin.value.joinCondition = value;
  },
});

// 获取所有可用的任务节点名称
const availableTaskNames = computed(() => {
  // 这里需要从工作流中获取所有任务节点的名称
  // 暂时返回空数组，实际实现需要从 store 中获取
  return [];
});

defineExpose({
  set(v: string) {
    activeKey.value = v === 'basic' ? 'basic' : 'script';
  },
});
</script>

<template>
  <Tabs v-model:active-key="activeKey" tab-position="left" type="card">
    <TabPane key="basic" tab="基本">
      <AForm
        v-if="parallelJoin"
        :label-col="{ span: 6 }"
        :model="parallelJoin"
        :wrapper-col="{ span: 18 }"
      >
        <AFormItem label="名称">
          <AInput
            v-model:value="parallelJoin.name"
            autocomplete="off"
            placeholder="汇聚任务名称"
            @blur="onNameChange"
          />
        </AFormItem>

        <AFormItem label="汇聚条件">
          <ASelect v-model:value="joinCondition" placeholder="选择汇聚条件">
            <ASelectOption :value="JoinConditionSubmit.All">
              等待所有任务完成
            </ASelectOption>
            <ASelectOption :value="JoinConditionSubmit.Any">
              等待任一任务完成
            </ASelectOption>
            <ASelectOption :value="JoinConditionSubmit.Custom">
              自定义条件
            </ASelectOption>
          </ASelect>
        </AFormItem>

        <AFormItem
          label="等待任务"
          v-if="joinCondition !== JoinConditionSubmit.Custom"
        >
          <CheckboxGroup v-model:value="waitForWorkTasks">
            <Checkbox
              v-for="taskName in availableTaskNames"
              :key="taskName"
              :value="taskName"
            >
              {{ taskName }}
            </Checkbox>
          </CheckboxGroup>
        </AFormItem>
      </AForm>
    </TabPane>

    <TabPane
      key="script"
      tab="自定义脚本"
      v-if="joinCondition === JoinConditionSubmit.Custom"
    >
      <h3>自定义汇聚条件</h3>
      <CodeEditor
        v-if="parallelJoin"
        v-model:code="customJoinScript"
        type="javascript"
        @focus="() => SetIntellisence(IntellisensType.ScriptDecision)"
      />
    </TabPane>
  </Tabs>
</template>
