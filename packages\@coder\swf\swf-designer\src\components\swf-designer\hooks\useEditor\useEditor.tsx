import type {
  ConditionDecisionSubmit,
  NodeSubmit,
  ParallelJoinNodeSubmit,
  ParallelSplitNodeSubmit,
  ScriptDecisionSubmit,
  StartNodeSubmit,
  WorkTaskSubmit,
} from '@coder/swf-api';

import type { Editor, NavigateKey } from '../../types';
import type {
  workProcessEventNameType,
  workTaskEventNameType,
} from '../eventBus';

import { BranchesOutlined, HomeOutlined } from '@ant-design/icons-vue';
import { workflowType } from '@coder/swf-api';

import { StartIcon } from '../../../icon';
import DebuggerDispose from '../../debugger/dispose.vue';
import FlowDiagarm from '../../diagram/index.vue';
import StartNodeStart from '../../editors/startNode/index.vue';
import { useTabsStore, useWorkProcessStore } from '../../stores';
import { useEditorNav } from '../useEditorNav';
import { addConditionEditor, addDecisionEditor } from './_useDecisionEditor';
import {
  addParallelJoinEditor,
  addParallelSplitEditor,
} from './_useParallel.tsx';
import {
  addWorkProcessFormDesigner,
  addWorkProcessManageFormDesigner,
  addWorkProcessScript,
  useWorkProcessEditor,
} from './_useWorkProcessEditor';
import { addWorkTaskEditor, addWorkTaskFormDesign } from './_useWorkTaskEditor';

const renderStartEditor = (startNode: StartNodeSubmit, storeId: string) => {
  return <StartNodeStart startNode={startNode} storeId={storeId} />;
};

export const useEditor = (storeId: string) => {
  const editorNavFactory = useEditorNav();
  const tabsStore = useTabsStore(storeId);

  /**
   * 开始节点编辑器
   */
  const addStartEditor = () => {
    const workProcessStore = useWorkProcessStore(storeId);
    const node = workProcessStore.workProcess.nodes.find(
      (f) => f.$type === workflowType.startNode,
    );
    if (!node) {
      throw new Error('没有开始节点');
    }

    const navKey = editorNavFactory.builder.start.editor();

    let editor = tabsStore.findEditor(navKey);
    if (editor) {
      tabsStore.currentEditor = navKey;
      return;
    }

    const startNode = node as StartNodeSubmit;
    editor = {
      close: true,
      component: () => renderStartEditor(startNode, storeId),
      icon: StartIcon,
      key: editorNavFactory.builder.start.editor(),
      title: '开始',
    } as Editor;

    tabsStore.addEditor(editor, false);
  };

  const addWorkTaskEventEditor = (
    workTask: WorkTaskSubmit,
    type: workTaskEventNameType,
  ) => {
    return addWorkTaskEditor(storeId, workTask, type);
  };

  const addEditorByNode = (node: NodeSubmit) => {
    switch (node.$type) {
      case workflowType.boolScriptDecision: {
        addDecisionEditor(storeId, node as ScriptDecisionSubmit);
        break;
      }
      case workflowType.conditionDecision: {
        addConditionEditor(storeId, node as ConditionDecisionSubmit);
        break;
      }
      case workflowType.parallelJoin: {
        addParallelJoinEditor(storeId, node as ParallelJoinNodeSubmit);
        break;
      }
      case workflowType.parallelSplit: {
        addParallelSplitEditor(storeId, node as ParallelSplitNodeSubmit);
        break;
      }
      case workflowType.startNode: {
        addStartEditor();
        break;
      }
      case workflowType.workTask: {
        addWorkTaskEditor(storeId, node as WorkTaskSubmit);
        break;
      }
      default: {
        throw new Error('这个方法支持startNode/Worktask/decision三种。');
      }
    }
  };
  const addWorkProcess = () => {
    const { render, setTab } = useWorkProcessEditor(storeId);

    const editor = {
      close: false,
      component: () => render(),
      ctrl: {
        setTab,
      },
      icon: HomeOutlined,
      key: editorNavFactory.builder.workProcess.editor(),
      title: '工作流设置',
    };
    tabsStore.addEditor(editor, false);
  };
  const addFlowDiagarm = () => {
    const render = () => {
      return <FlowDiagarm storeId={storeId} />;
    };
    const editor = {
      close: false,
      component: () => render(),

      icon: BranchesOutlined,
      key: 'dialog-flow',
      title: '',
    };
    tabsStore.addEditor(editor, false);
  };

  const addWorkActivityDisposeEditor = (disposeUser: string) => {
    const editor = {
      close: true,
      component: () => {
        return <DebuggerDispose disposeUser={disposeUser} storeId={storeId} />;
      },
      icon: HomeOutlined,
      key: editorNavFactory.builder.workTask.debugger(),
      title: '调试-处理form',
    } as Editor;

    tabsStore.addEditor(editor, false, 0);
  };

  return {
    addDecisionCodeEditor: (decision: ScriptDecisionSubmit) =>
      addDecisionEditor(storeId, decision, '脚本'),
    addDecisionEditor: (decision: ScriptDecisionSubmit) =>
      addDecisionEditor(storeId, decision),
    addEditorByNode,

    addFlowDiagarm,
    addStartEditor,
    addWorkActivityDisposeEditor,
    addWorkProcess,
    addWorkProcessFormDesigner: () => {
      addWorkProcessFormDesigner(storeId, tabsStore);
    },
    addWorkProcessManageFormDesigner: () => {
      addWorkProcessManageFormDesigner(storeId, tabsStore);
    },
    /**
     * 添加工作流脚本编辑器
     * @param type 编辑器类型
     * @returns {void}
     */
    addWorkProcessScript: (type: workProcessEventNameType): void =>
      addWorkProcessScript(type, tabsStore),
    /**
     * 添加工作任务。
     * @param workTask
     * @returns {void}
     */
    addWorkTaskAssignerEditor: (workTask: WorkTaskSubmit): void =>
      addWorkTaskEditor(storeId, workTask, 'assigner'),
    /**
     * 添加worktask编辑器
     * @param workTask
     * @returns {void}
     */
    addWorkTaskEditor: (workTask: WorkTaskSubmit) =>
      addWorkTaskEditor(storeId, workTask),
    /**
     * 添加workTask脚本的编辑器。
     */
    addWorkTaskEventEditor,
    addWorkTaskFormDesign: (workTask: WorkTaskSubmit) => {
      addWorkTaskFormDesign(storeId, workTask);
    },
    goto: (key: NavigateKey) => {
      const { findSwfNode } = useEditorNav();
      const navInfo = findSwfNode(key);
      if (navInfo.isWorkProcess) {
        tabsStore.currentEditor = key;
      } else {
        const workProcessStore = useWorkProcessStore(storeId);
        const node = workProcessStore.workProcess.nodes.find(
          (_) => _.name === navInfo.nodeName,
        );
        if (node)
          switch (node.$type) {
            case workflowType.boolScriptDecision: {
              addDecisionEditor(storeId, node as any);
              break;
            }
            case workflowType.startNode: {
              addStartEditor();
              break;
            }
            case workflowType.workTask: {
              addWorkTaskEditor(storeId, node as any);
              break;
            }
          }
      }
    },
  };
};
