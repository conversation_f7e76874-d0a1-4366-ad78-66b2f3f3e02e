import type {
  ConditionDecisionSubmit,
  EndNodeSubmit,
  ParallelJoinNodeSubmit,
  ParallelSplitNodeSubmit,
  ScriptAssignerSubmit,
  ScriptDecisionSubmit,
  StartNodeSubmit,
  WorkProcessSubmit,
  WorkTaskSubmit,
} from '@coder/swf-api';

import {
  AssignScopeType,
  JoinConditionSubmit,
  LogLevel,
  NextTaskPerformers,
  workflowType,
} from '@coder/swf-api';
import { uniqueId } from 'lodash-es';

export const WorkflowFactory = {
  createConditionDecision: (): ConditionDecisionSubmit => {
    return {
      $type: workflowType.conditionDecision,
      id: 0,
      matchDescription: '是',
      name: uniqueId('decision'),
      script: '',
      settings: [],
    } as ConditionDecisionSubmit;
  },
  createDefaultTask: () => {
    const result = {
      $type: workflowType.workTask,
      assigner: {
        $type: workflowType.scriptAssigner,
        assignScopeType: AssignScopeType.OneOfTem,
        script: '',
      } as <PERSON><PERSON>tAssignerSubmit,
      canGiveUp: false,
      id: 0,
      name: uniqueId('workTask-'),
      nextTaskPerformers: NextTaskPerformers.Manual,
      suggestionComment: '',
      workActivityCompleteScript: { script: '' },
      workTaskCompleteScript: { script: '' },
      workTaskStartScript: { script: '' },
    } as WorkTaskSubmit;
    return result;
  },
  createScriptDecision: (): ScriptDecisionSubmit => {
    return {
      $type: workflowType.boolScriptDecision,
      elseDescription: '否',
      id: 0,
      matchDescription: '是',
      name: uniqueId('decision'),
      script: '',
    };
  },
  createParallelJoin: (): ParallelJoinNodeSubmit => {
    return {
      $type: workflowType.parallelJoin,
      id: 0,
      name: uniqueId('parallelJoin'),
      customJoinScript: '',
      joinCondition: JoinConditionSubmit.All,
      waitForWorkTasks: [],
    } as ParallelJoinNodeSubmit;
  },
  createParallelSplit: (): ParallelSplitNodeSubmit => {
    return {
      $type: workflowType.parallelSplit,
      id: 0,
      name: uniqueId('parallelSplit'),
    } as ParallelSplitNodeSubmit;
  },
  createWorkProcess: (): WorkProcessSubmit => {
    return {
      canBeDeleteWorkActivityCount: 0,
      enable: false,
      formManageDesign: undefined,
      globalScript: '',
      group: undefined,
      id: 0,
      logLevel: LogLevel.Critical,
      name: '工作流程-1',
      nodes: [
        {
          $type: workflowType.endNode,

          name: '结束',
        } as EndNodeSubmit,
        {
          $type: workflowType.startNode,
          name: '开始',
          nextNodeName: '结束',
        } as StartNodeSubmit,
      ],

      overWrite: true,
      plugins: undefined,
      updateTimeOffset: undefined,
      version: 1,
    } as WorkProcessSubmit;
  },
};
