/** 节点提交 */

export type NodeSubmit = {
  /** 类型
   */
  $type: string;
  /** id
   */
  id: number;
  /** 节点名称
   */
  name: string;
};
export type EndNodeSubmit = NodeSubmit & {};
export type StartNodeSubmit = SingleOutputNodeSubmit & {};

export interface MultiOutputNodeSubmit extends NodeSubmit {
  nextNodeNames: string[];
}

export interface SingleOutputNodeSubmit extends NodeSubmit {
  /** 下一个节点
   */
  nextNodeName?: string | undefined;
}
