<script setup lang="ts">
import type { ContractListItem, ContractSearch } from '@hai-an/contract-api';
import type { SelectValue } from 'ant-design-vue/es/select';

import { computed, onMounted, reactive, ref } from 'vue';

import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  createContractApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as AButton,
  InputGroup as AInputGroup,
  Select as ASelect,
  Tooltip as ATooltip,
  message,
} from 'ant-design-vue';

import ContractsAdd from './components/add.vue';

type SelectOptionType = {
  contract: ContractListItem;
  label: string;
  value: string;
};
const props = defineProps({
  bookType: {
    default: () => '',
    type: String,
  },
  contractType: {
    default: () => '',
    type: String,
  },
  isLock: { type: Boolean },
  isMaster: { type: <PERSON>olean },
  isWorkloadLock: { type: <PERSON>ole<PERSON> },
  modelValue: { default: '', type: String },
  /**
   * 是否允许添加合同
   */
  addContract: { type: Boolean, default: true },
  orgPath: {
    default: () => '',
    type: String,
  },
});
const emit = defineEmits<{
  (e: 'change' | 'update:modelValue', val: ContractListItem | undefined): void;
  (e: 'update:modelValue', val: string): void;
}>();

const api = createContractApi(options.request, options.path);

const addOpen = ref(false);

const loading = ref(false);

const getBookType = () => {
  if (props.bookType === '总公司签订') {
    return 0;
  } else if (props.bookType === '分公司签订') {
    return 1;
  } else {
    return 2;
  }
};

const getContractType = () => {
  return props.contractType === '收款合同' ? 0 : 1;
};

const searchForm = reactive<ContractSearch>({
  bookType: getBookType(),
  contractType: getContractType(),
  isDeleted: false,
  isLock: props.isLock,
  isMaster: props.isMaster,
  isWorkloadLock: props.isWorkloadLock,
  orgPath: props.orgPath,
  page: 1,
  pageSize: 50,
});

const contractInfo = computed({
  get: () => {
    return props.modelValue;
  },
  set: (v) => {
    emit('update:modelValue', v);
  },
});

const datas = reactive<SelectOptionType[]>([]);
const allDatas = reactive<ContractListItem[]>([]);

const handleChange = (value: SelectValue) => {
  const contract = datas.find((item) => item.value === value);
  emit('change', contract?.contract);
};

const toSearch = (code = '') => {
  loading.value = true;
  api.masterList(searchForm).then((res) => {
    const contracts: SelectOptionType[] = [];
    allDatas.splice(0);
    allDatas.push(...res);
    res.forEach((element) => {
      if (code && element.code === code) {
        emit('update:modelValue', element.code);
        emit('change', { ...element });
      }
      contracts.push({
        contract: element,
        label: element.code,
        value: element.code,
      });
    });
    datas.splice(0);
    datas.push(...contracts);
    loading.value = false;
  });
};
const handleSearch = (val: string) => {
  searchForm.code = val;
  toSearch();
};
const addForm = ref();

const onAdd = () => {
  addOpen.value = true;
};
const doSave = (formState: any) => {
  api.save({ ...formState, orgPath: props.orgPath }).then((res) => {
    if (res.success) {
      message.success(res.message);
      addForm.value?.handelCancel();
      emit('update:modelValue', formState.code);
      searchForm.code = formState.code;
      toSearch(formState.code);
    } else {
      message.error(res.message);
    }
  });
};
const addContract = computed(() => {
  return props.addContract;
});
onMounted(() => {
  contractInfo.value = props.modelValue;
  toSearch();
});
</script>
<template>
  <div>
    <AInputGroup compact>
      <ASelect
        v-model:value="contractInfo"
        show-search
        placeholder="请输入合同号"
        :default-active-first-option="false"
        :loading="loading"
        :filter-option="false"
        style="min-width: 100px"
        :not-found-content="null"
        :options="datas"
        @change="handleChange"
        @search="handleSearch"
        allow-clear
      >
        <template #suffixIcon><SearchOutlined /></template>
      </ASelect>
      <ATooltip title="新增合同" v-if="addContract">
        <AButton type="link" @click="onAdd">
          <PlusOutlined />
        </AButton>
      </ATooltip>
    </AInputGroup>
    <ContractsAdd ref="addForm" @do-save="doSave" v-model:open="addOpen" />
  </div>
</template>
