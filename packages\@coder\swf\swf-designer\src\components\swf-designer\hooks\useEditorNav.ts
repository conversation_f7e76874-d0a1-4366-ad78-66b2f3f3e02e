import type {
  ConditionDecisionSubmit,
  NodeSubmit,
  ParallelJoinNodeSubmit,
  ParallelSplitNodeSubmit,
  ScriptDecisionSubmit,
  WorkTaskSubmit,
} from '@coder/swf-api';

import type { NavigateKey } from '../types';
import type {
  workProcessEventNameType,
  workTaskEventNameType,
} from './eventBus';

import { workflowType } from '@coder/swf-api';

import {
  workProcessEventName,
  WorkTaskAssignKeyName,
  workTaskEventName,
} from './eventBus';

export type NavigateKeyInfo = {
  eventName?: workProcessEventNameType | workTaskEventNameType;
  isWorkProcess: boolean;
  nodeName?: string;
};
export const useEditorNav = () => {
  const builder = {
    parallel: {
      join: (submit: ParallelJoinNodeSubmit): NavigateKey => {
        return `|${submit.name}|`;
      },
      split: (submit: ParallelSplitNodeSubmit): NavigateKey => {
        return `-${submit.name}-`;
      },
    },
    decision: {
      bool: {
        editor: (swfNode: ScriptDecisionSubmit): NavigateKey => {
          return `<${swfNode.name}>`;
        },
        script: (swfNode: ScriptDecisionSubmit) => {
          return `<${swfNode.name}>/脚本`;
        },
      },

      condition: {
        editor: (swfNode: ConditionDecisionSubmit): NavigateKey => {
          return `{${swfNode.name}}`;
        },
        script: (swfNode: ConditionDecisionSubmit) => {
          return `{${swfNode.name}}/脚本`;
        },
      },

      /**
       * @deprecated use Bool.editor
       * @param swfNode
       * @returns
       */
      editor: (swfNode: ScriptDecisionSubmit): NavigateKey => {
        return `<${swfNode.name}>`;
      },
      /**
       *@deprecated use Bool.script
       * @param swfNode
       * @returns
       */
      script: (swfNode: ScriptDecisionSubmit) => `<${swfNode.name}>/脚本`,
    },
    start: {
      editor: () => {
        return '(开始)';
      },
    },
    workProcess: {
      cancelCode: (): NavigateKey => {
        return `workProcess/${workProcessEventName.cancel}`;
      },
      completeCode: (): NavigateKey => {
        return `workProcess/${workProcessEventName.complete}`;
      },
      editor: (): NavigateKey => {
        return 'workProcess/editor';
      },
      formDesign: (): NavigateKey => {
        return `workProcess/#designForm`; // 工作流 formDesign 事件
      },
      manageFormDesign: (): NavigateKey => {
        return `workProcess/#managerForm`; // 工作流 formDesign 事件
      },
      startCode: (): NavigateKey => {
        return `workProcess/${workProcessEventName.start}`;
      },
    },
    workTask: {
      assigner: (swfNode: WorkTaskSubmit): NavigateKey => {
        const nameAssign =
          swfNode.assigner.$type === workflowType.scriptAssigner
            ? WorkTaskAssignKeyName.script
            : WorkTaskAssignKeyName.select;
        return `[${swfNode.name}]/分配/@${nameAssign}`;
      },
      commandKey: (workTask: WorkTaskSubmit, cmd: string): NavigateKey => {
        return `[${workTask.name}]/${cmd}`;
      },
      debugger: (): string => {
        return 'work-activity-debugger';
      },
      editor: (workTask: WorkTaskSubmit): NavigateKey => {
        return `[${workTask.name}]`;
      },
      formDesign: (workTask: WorkTaskSubmit): NavigateKey => {
        return `[${workTask.name}]/#designForm`;
      },
      script: (workTask: WorkTaskSubmit): NavigateKey => {
        return `[${workTask.name}]/脚本`;
      },
      scriptEnd: (workTask: WorkTaskSubmit): NavigateKey => {
        return `[${workTask?.name}]/${workTaskEventName.complete}`;
      },
      scriptStart: (workTask: WorkTaskSubmit): NavigateKey => {
        return `[${workTask?.name}]/${workTaskEventName.start}`;
      },
      scriptWorkActivityEnd: (workTask: WorkTaskSubmit): NavigateKey => {
        return `[${workTask?.name}]/${workTaskEventName.workActivityComplete}`;
      },
    },
  };
  const getName = (key: NavigateKey): string => {
    // eslint-disable-next-line regexp/no-unused-capturing-group
    const matcher = /<([^<>]+)>|\[([^[]+)\]|\(([^()]+)\)/.exec(key);
    if (!matcher) return '';
    return matcher[2] ?? '';
  };

  const findSwfNode = (key: NavigateKey): NavigateKeyInfo => {
    const result = { isWorkProcess: false } as NavigateKeyInfo;
    const firstChar = key[0];
    if (firstChar === 'w') {
      result.isWorkProcess = true;
      return result;
    }

    result.nodeName = getName(key);
    return result;
  };

  return {
    build: (node: NodeSubmit) => {
      switch (node.$type) {
        case workflowType.boolScriptDecision: {
          return builder.decision.bool.editor(node as any);
        }

        case workflowType.conditionDecision: {
          return builder.decision.condition.editor(node as any);
        }

        case workflowType.workTask: {
          return builder.workTask.editor(node as any);
        }
        default: {
          throw new Error(`未知的节点类型:${node.$type}`);
        }
      }
    },
    builder,
    findSwfNode,
    getName,
  };
};
