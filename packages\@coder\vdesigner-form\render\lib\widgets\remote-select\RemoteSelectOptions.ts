export class RemoteSelectOptions {
  [key: string]: any;
  activatedEvent: string | undefined;
  allowClear: boolean = false;
  changeEvent?: string;
  deactivatedEvent: string | undefined;
  disabled?: boolean;
  hidden: boolean = false;
  label: string = '远程选择';
  mobileOffset: string | undefined;
  mobileWidth?: string | undefined;
  mode: 'combobox' | 'multiple' | 'tags' | undefined;
  mountedEvent: string | undefined;
  multi: boolean = false;
  options: Array<{ label: string; value: string }> = new Array<{
    label: string;
    value: string;
  }>();
  padOffset?: string | undefined;
  padWidth?: string | undefined;
  pcOffset?: string | undefined;
  pcWidth?: string | undefined;
  unmountedEvent: string | undefined;
}
