import type {
  ConditionDecisionSubmit,
  MultiOutputNodeSubmit,
  NodeSubmit,
  ParallelJoinNodeSubmit,
  ParallelSplitNodeSubmit,
  ScriptDecisionSubmit,
  SingleOutputNodeSubmit,
  WorkProcessSubmit,
  WorkTaskSubmit,
} from '@coder/swf-api';

import type { DesignerPropsType } from '../types';

import { computed, reactive, ref } from 'vue';

import { crateDefineApi } from '@coder/swf-api';
import { swfOption } from '@coder/swf-render';
import { message } from 'ant-design-vue';
import { defineStore } from 'pinia';

import { WorkflowFactory } from '../hooks/util';
import { useDiagramStore } from './diagram';

const _storeMap = new Map<string, any>();

/**
 * store的定义。
 * 隐含了pinina store的方法。但是依然可以调用
 */
type DesignerWorkProcessStoreType = {
  checkVersion: () => void;
  createConditionDecision: () => ConditionDecisionSubmit;
  createParallelJoin: () => ParallelJoinNodeSubmit;
  createParallelSplit: () => ParallelSplitNodeSubmit;
  /**
   * 创建一个判断其，并且更新Store
   * @returns {ScriptDecisionSubmit}
   */
  createScriptDecision: () => ScriptDecisionSubmit;
  /**
   * 创建一个工作任务，并且更新store
   * @returns {WorkTaskSubmit}
   */
  createWorkTask: () => WorkTaskSubmit;
  linkChange: () => void;
  loading: {
    loadWorkProcess: boolean;
    saveLoading: boolean;
  };
  nodeNameChange: (srcName: string, name: string) => void;
  processingCount: number;
  reload: () => Promise<WorkProcessSubmit>;
  removeNode: (node: NodeSubmit) => void;
  save: () => void;
  workProcess: WorkProcessSubmit;
  workProcessId: number;
};
const _createStoreId = (storeId: string) => {
  return `${storeId}/work-process`;
};
/**
 * 创建与当前设计器的store
 * @param workProcessStoreId sotreid 通过 useDesigner获得
 * @param wpId 工作流定义id
 * @returns 返回一个用于创建store的方法
 */
const createWorkProcessStore = (workProcessStoreId: string, wpId?: number) => {
  const defineApi = crateDefineApi(swfOption.request, swfOption.host);
  const workProcessId = ref(wpId);

  const state = defineStore(workProcessStoreId, () => {
    const workProcessData = reactive<{ workProcess: WorkProcessSubmit }>({
      workProcess: WorkflowFactory.createWorkProcess(),
    });
    const workProcess = computed(() => workProcessData.workProcess);

    const processingCount = ref(0);

    const loading = reactive({
      loadWorkProcess: false,
      saveLoading: false,
    });
    const checkVersion = () => {
      defineApi
        .checkVersion(workProcess.value.id, workProcess.value.version)
        .then((resp) => {
          processingCount.value = resp.processing;
          workProcess.value.overWrite = !(resp.processing > 1);
        });
    };
    const reload = (): Promise<WorkProcessSubmit> => {
      const id = workProcessId.value;
      if (!id) {
        workProcessData.workProcess = WorkflowFactory.createWorkProcess();
        return Promise.resolve(workProcessData.workProcess);
      }
      loading.saveLoading = true;

      return defineApi.getById(id).then((resp) => {
        workProcessData.workProcess = resp;
        loading.saveLoading = false;
        workProcess.value.overWrite = !workProcess.value.enable;
        checkVersion();
        return workProcessData.workProcess;
      });
    };

    const save = () => {
      loading.loadWorkProcess = true;
      defineApi.save(workProcess.value).then((resp) => {
        message.success(resp.message);
        loading.loadWorkProcess = false;
      });
    };

    const enableSaveBtn = computed(() => {
      return false;
    });
    const storeId = workProcessStoreId.split('/')[0];
    if (storeId === undefined) throw new Error('错误的storeId');
    return {
      checkVersion,
      createConditionDecision: (): ConditionDecisionSubmit => {
        const decision = WorkflowFactory.createConditionDecision();
        const length = workProcess.value.nodes.push(decision);
        return workProcess.value.nodes[length - 1] as ConditionDecisionSubmit;
      },
      createScriptDecision: (): ScriptDecisionSubmit => {
        /**
         * 这里反直觉。
         * 当执行第二行的时候，第一行的 decision 对象已经和nodes中最后一个无关系。因为要它变为一个reactive对象。
         * 所以第三行代码，需要返回nodes的最后一个。而不能返回workTask
         */
        const decision = WorkflowFactory.createScriptDecision();
        const length = workProcess.value.nodes.push(decision);
        return workProcess.value.nodes[length - 1] as ScriptDecisionSubmit;
      },
      createWorkTask: (): WorkTaskSubmit => {
        /**
         * 这里反直觉。
         * 当执行第二行的时候，第一行的 workTask 对象已经和nodes中最后一个无关系。因为要它变为一个reactive对象。
         * 所以第三行代码，需要返回nodes的最后一个，而不能直接返回workTask
         */
        const workTask = WorkflowFactory.createDefaultTask();
        const length = workProcess.value.nodes.push(workTask);
        return workProcess.value.nodes[length - 1] as WorkTaskSubmit;
      },
      createParallelJoin: () => {
        const parallelJoin = WorkflowFactory.createParallelJoin();
        const length = workProcess.value.nodes.push(parallelJoin);
        return workProcess.value.nodes[length - 1] as ParallelJoinNodeSubmit;
      },
      createParallelSplit: () => {
        const parallelSplit = WorkflowFactory.createParallelSplit();
        const length = workProcess.value.nodes.push(parallelSplit);
        return workProcess.value.nodes[length - 1] as ParallelSplitNodeSubmit;
      },
      enableSaveBtn,
      linkChange: () => {
        // 更新图形菜单
        const diagramStore = useDiagramStore(storeId);
        diagramStore.build();
      },

      loading,
      nodeNameChange: (srcName: string, name: string) => {
        workProcessData.workProcess.nodes.forEach((node) => {
          const singleNode = node as SingleOutputNodeSubmit;
          const multiNode = node as MultiOutputNodeSubmit;

          if (singleNode.nextNodeName === srcName) {
            singleNode.nextNodeName = name;
          } else if (multiNode.nextNodeNames) {
            for (let i = 0; i < multiNode.nextNodeNames.length; i++) {
              if (multiNode.nextNodeNames[i] === srcName) {
                multiNode.nextNodeNames[i] = name;
                break;
              }
            }
          }
        });
      },

      processingCount,
      reload,
      removeNode: (node: NodeSubmit) => {
        const index = workProcess.value.nodes.findIndex(
          (_) => _.name === node.name,
        );
        if (index === undefined) {
          message.error({ content: `删除失败:${node.name}` });
        } else {
          workProcess.value.nodes.splice(index, 1);
        }
      },
      save,
      workProcess,

      workProcessData,
      workProcessId,
    };
  });

  return state;
};

/**
 * 获取一个与storeId有关的worProcess-store,用于对workProcess对象进行设计的对象
 * @param storeId
 * @param props
 * @returns 返回store
 */
export const useWorkProcessStore = (
  storeId: string,
  props?: DesignerPropsType,
): DesignerWorkProcessStoreType => {
  if (storeId === undefined || storeId === '')
    throw new Error('renderId is undefined');

  const designerStoreId = _createStoreId(storeId);
  if (_storeMap.has(designerStoreId)) {
    return _storeMap.get(designerStoreId) as DesignerWorkProcessStoreType;
  }

  // console.log('初始化 work process store');
  const useStore = createWorkProcessStore(designerStoreId, props?.id);

  const store = useStore();
  _storeMap.set(designerStoreId, store);

  return store as DesignerWorkProcessStoreType;
};
