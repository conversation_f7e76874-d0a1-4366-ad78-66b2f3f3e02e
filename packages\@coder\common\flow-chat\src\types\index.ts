export interface FlowNode {
  data?: any;
  height?: number;
  id: string;
  label: string;
  type?:
    | 'custom'
    | 'decision'
    | 'end'
    | 'parallelJoin'
    | 'parallelSplit'
    | 'process'
    | 'start';
  width?: number;
  x?: number;
  y?: number;
}

export interface FlowEdge {
  id: string;
  label?: string;
  source: string;
  target: string;
  type?: 'bezier' | 'step' | 'straight';
}

export interface FlowChatData {
  edges: FlowEdge[];
  nodes: FlowNode[];
}

export interface Position {
  x: number;
  y: number;
}

export interface NodeClickEvent {
  event: MouseEvent;
  node: FlowNode;
}

export interface EdgeClickEvent {
  edge: FlowEdge;
  event: MouseEvent;
}

export type LayoutDirection = 'bt' | 'lr' | 'rl' | 'tb';

export interface FlowChatProps {
  autoLayout?: boolean;
  data: FlowChatData;
  layoutDirection?: LayoutDirection;
  maxZoom?: number;
  minZoom?: number;
  nodeHeight?: number;
  nodeWidth?: number;
  readonly?: boolean;
  selectNode?: string | string[]; // 新增：外部指定要高亮的节点ID，支持单个或多个
  zoomable?: boolean;
}
