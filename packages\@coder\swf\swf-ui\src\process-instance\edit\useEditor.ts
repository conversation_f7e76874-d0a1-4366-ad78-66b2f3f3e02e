import type { ProcessInstanceViewModel } from '@coder/swf-api';

import { computed, ref } from 'vue';

import { createWorkflowApi } from '@coder/swf-api';
import { showSubmitResult, swfOption } from '@coder/swf-render';

const useEditor = (processInstanceId: number) => {
  const workflowApi = createWorkflowApi(swfOption.request, swfOption.host);
  const processInstance = ref<ProcessInstanceViewModel>();

  const reload = () => {
    workflowApi.get(processInstanceId).then((resp) => {
      processInstance.value = resp;
    });
  };
  const formData = computed(() => {
    return processInstance.value?.form;
  });
  const formManagerDesigner = computed(() => {
    if (!processInstance.value?.formManageDesign) {
      return {};
    }
    return JSON.parse(processInstance.value.formManageDesign);
  });
  const hasManagerFormDesigner = computed(() => {
    return !!processInstance.value?.formManageDesign;
  });
  const sourceCode = computed({
    get: () => {
      return processInstance.value?.form || {};
    },
    set: (value) => {
      if (!processInstance.value) {
        return;
      }
      processInstance.value.form = value;
    },
  });
  const save = () => {
    if (!processInstance.value) {
      return;
    }
    const form = processInstance.value.form;
    workflowApi.saveForm(processInstanceId, form).then((resp) => {
      showSubmitResult(resp);
    });
  };

  return {
    formData,
    formManagerDesigner,
    hasManagerFormDesigner,
    processInstance,
    reload,
    save,
    sourceCode,
  };
};

export default useEditor;
