<script lang="ts" setup>
import type { AttachPropsType } from './types';

import { computed, onMounted, reactive } from 'vue';

import { RefreshIcon } from '@vben-core/icons';

// import { CoderUploader } from '@coder/file-uploador';
import { UploadButton } from '@coder/file-upload';
import { createWorkflowApi } from '@coder/swf-api';
// import { CategorySelector } from '@hai-an/contract-ui';
import { Button, Space, Table } from 'ant-design-vue';

import { swfOption } from '../../../swfOption';
import createColumns from './infoColumn';
import { useAttachments } from './useAttachment';

const props = defineProps<AttachPropsType>();
const { del, fileList, list } = useAttachments(props);

const columns = reactive(
  createColumns((fileAttach) => {
    del(fileAttach.id);
  }, props.isEdit),
);

const headers = computed(() => {
  return {
    Authorization: `Bearer ${swfOption.getToken()}`,
  };
});

const uploadUrl = computed(() => {
  const api = createWorkflowApi(swfOption.request, swfOption.host);
  const result = api.uploadAttachmentUrl(
    Number.parseInt(props.processInstanceId.toString()),
  );
  if (result.startsWith('http')) {
    return result;
  }
  return `api/${result}`;
});

onMounted(() => {
  list();
});

const reLoad = () => {
  list();
};

defineExpose({ reLoad });
</script>

<template>
  <div>
    <Space v-if="props.isEdit" style="margin-bottom: 15px">
      <Button type="primary" @click="list()">
        <RefreshIcon />
        刷新
      </Button>

      <UploadButton
        :upload-url="uploadUrl"
        :headers="headers"
        file-form-key="file"
        @success="reLoad"
      />
    </Space>

    <Table
      :columns="columns"
      :data-source="fileList"
      :pagination="false"
      :row-key="(data: any) => data.fileName"
      bordered
      class="ant-table-striped"
      size="small"
      :scroll="{ y: 240 }"
    />
  </div>
</template>
