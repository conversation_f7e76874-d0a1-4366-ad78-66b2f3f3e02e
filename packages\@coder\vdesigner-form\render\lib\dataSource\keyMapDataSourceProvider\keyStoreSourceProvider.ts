import type {
  IDataSourceProvider,
  useRenderStore,
  WidgetOptionEditorSetting,
} from '@coder/vdesigner-core';

import type { Component } from 'vue';

import type { RequestClient } from '@vben/request';

import { DatabaseOutlined } from '@ant-design/icons-vue';
import {
  renderOptions,
  stringEditor,
  useTemplateStore,
} from '@coder/vdesigner-core';

const createEditor = () => {
  const editor = stringEditor('DataKey');
  editor.valueFrom = ['formData', 'code', 'value'];

  return {
    DataKey: editor,
  };
};

export class KeyStoreageDataSourceProvider implements IDataSourceProvider {
  icon?: Component | string | undefined = DatabaseOutlined;
  label: string = '数据映射';
  optionEditor: Record<string, WidgetOptionEditorSetting>;
  type: string = 'KeyStorageDataSource';
  constructor() {
    this.optionEditor = createEditor();
  }
  getData(data: any, _state: ReturnType<typeof useRenderStore>): Promise<any> {
    const templateStore = useTemplateStore(
      `${renderOptions.storageHost}`,
      renderOptions.request as RequestClient,
    );

    return templateStore.getTemplate(data.DataKey);
  }
}
