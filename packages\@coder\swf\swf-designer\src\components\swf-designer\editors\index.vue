<script setup lang="ts">
import type { DesignerTranslatePropsType } from '../types';

import { computed, ref } from 'vue';

import { HomeOutlined } from '@ant-design/icons-vue';
import {
  Modal as AModal,
  Radio as ARadio,
  RadioGroup as ARadioGroup,
  TabPane as ATabPane,
  Tabs as ATabs,
} from 'ant-design-vue';
import { isFunction } from 'lodash-es';

import { useSwfDesigner } from '../hooks/index';
// 各种编辑器
// 编辑器的附加方法
import { useTabsStore, useWorkProcessStore } from '../stores';

const props = defineProps<DesignerTranslatePropsType>();
const workProcessStore = useWorkProcessStore(props.storeId);
const tabsStore = useTabsStore(props.storeId);
const {
  createConditionDecision,
  createScriptDecision,
  createTask,
  selectNode,
  createParallelJoin,
  createParallelSplit,
} = useSwfDesigner(props.storeId);
const editors = computed(() => tabsStore.editors);

/** workTask 的tab */

const workProcess = computed(() => workProcessStore.workProcess);

/** 创建类型 */
const visibleCreateModel = ref(false);
const createType = ref('workTask');

/** 创建Node */
const onAddNode = () => {
  switch (createType.value) {
    case 'conditionDecision': {
      const wt = createConditionDecision();
      selectNode(wt);
      break;
    }
    case 'decision': {
      const decision = createScriptDecision();
      selectNode(decision);
      break;
    }
    case 'parallelJoin': {
      const parallelJoin = createParallelJoin();
      selectNode(parallelJoin);
      break;
    }
    case 'parallelSplit': {
      const parallelSplit = createParallelSplit();
      selectNode(parallelSplit);
      break;
    }
    case 'workTask': {
      const wt = createTask();
      selectNode(wt);
      break;
    }
  }
  visibleCreateModel.value = false;
};

/**
 * tab点击 关闭 icon
 */
const onEdit = (targetKey: any, action: 'add' | 'remove') => {
  if (action === 'add') {
    visibleCreateModel.value = true;
    return;
  }

  tabsStore.removeEditor(targetKey);
};
</script>

<template>
  <ATabs
    v-if="workProcess"
    v-model:active-key="tabsStore.currentEditor"
    size="small"
    type="editable-card"
    @edit="onEdit"
  >
    <!-- <ATabPane key="diagramKey" :closable="false" tab="图">
      <Diagram :store-id="props.storeId" />
    </ATabPane> -->
    <ATabPane
      v-for="editor in editors"
      :key="editor.key"
      :closable="editor.close ?? true"
    >
      <template #tab>
        <span>
          <component :is="editor.icon" v-if="editor.icon" />
          <HomeOutlined v-else />
        </span>
        {{ isFunction(editor.title) ? (editor as any).title() : editor.title }}
      </template>

      <component :is="editor.component" />
    </ATabPane>
  </ATabs>

  <AModal
    v-model:open="visibleCreateModel"
    cancel-text="取消"
    ok-text="创建"
    title="新建"
    @ok="onAddNode"
  >
    <ARadioGroup v-model:value="createType" name="radioGroup">
      <ARadio value="workTask">工作任务</ARadio>
      <ARadio value="conditionDecision">多条件判断器</ARadio>
      <ARadio value="parallelSplit">同步任务</ARadio>
      <ARadio value="parallelJoin">汇聚任务</ARadio>
      <!-- <ARadio value="decision">工作判断器（尽量不要使用）</ARadio> -->
    </ARadioGroup>
  </AModal>
</template>
