<script lang="ts" setup>
import type { WidgetPropsType } from '@coder/vdesigner-core';

import { computed } from 'vue';

import { LabelOptions, useRenderStore, useWidget } from '@coder/vdesigner-core';
import { Button, Modal } from 'ant-design-vue';

import FormItem from '../formItem/formItem.vue';

const props = defineProps<WidgetPropsType>();
const { formData, getWidgetOption } = useWidget(props);
const store = useRenderStore(props.renderId);
const options = props.widget.options as LabelOptions;

const content = getWidgetOption<string>('content');
const contentResult = computed(() => {
  try {
    const _con = content.value;
    return LabelOptions.getContent(
      _con,
      props.widget.options as LabelOptions,
      formData.value,
      store.cfg,
    );
  } catch (error) {
    console.warn(' contentResult error',error);
    return '设计器label';
  }
});

const isDev = computed(() => {
  return store.isDev;
});
const showContent = (e: Event) => {
  e.stopPropagation();
  e.preventDefault();
  if (isDev.value) {
    Modal.info({
      content: options.content,
      title: 'Label',
      width: 600,
    });
  }
};
const hidden = getWidgetOption<boolean>('hidden');
</script>

<template>
  <FormItem
    :render-id="props.renderId"
    :widget="props.widget"
    :parent-widget="props.parentWidget"
    v-if="!hidden || store.isDesign"
  >
    <Button v-if="isDev" @click="showContent" type="link">
      {{ '空值' }}
    </Button>
    <span v-if="store.isDesign && options.labelHidden">label-占位</span>
    <span class="ant-form-text">{{ contentResult }} </span>
  </FormItem>
</template>
